local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);

local ToolAnimator = {};
ToolAnimator.__index = ToolAnimator;

local placeholderAnimation, placeholderTrack;

function ToolAnimator.onRequire()
    placeholderAnimation = Instance.new("Animation");
    placeholderAnimation.AnimationId = "rbxassetid://14884515645";

    placeholderTrack = script.Humanoid:LoadAnimation(placeholderAnimation);
    placeholderTrack.Name = "PlaceholderTrack";
end

function ToolAnimator.new(animator)
    local self = {
        Animator = animator;
        State = "";
        Anims = {};
        StateList = {};

        TrackEvent = shared.EventSignal.new("ToolAnimatorTrackEvent");
    };

    setmetatable(self, ToolAnimator);
    return self;
end

function ToolAnimator:LoadToolAnimations(animationPackage, setDefaultState, prefabs)
	for animName, animLib in pairs(animationPackage) do
        local animPacket = {};

        for idKey, idList in pairs(animLib) do
            local stateName = "";
            if idKey:sub(1, 2) == "Id" then
                stateName = idKey:sub(3, #idKey);
            else
                continue;
            end

            idList = typeof(idList) == "table" and idList or {idList};

            animPacket[stateName] = {};
            table.insert(self.StateList, stateName);

            for a=1, #idList do
                if typeof(idList[a]) ~= "number" then continue end;
                local animationId = "rbxassetid://"..idList[a];
               
                local fileName = `{animationId}:{animName}:{stateName}`;
                local animationFile = self.Animator:FindFirstChild(fileName) or Instance.new("Animation"); 
                animationFile.Name = fileName;
                animationFile.AnimationId = animationId;
                animationFile.Parent = self.Animator;

                local track: AnimationTrack = self.Animator:LoadAnimation(animationFile);
                
                if animLib.Looped ~= nil then
                    track.Looped = animLib.Looped == true;
                end
                
                if string.match(animName, "Core") == nil then
                    track.Priority = Enum.AnimationPriority.Action2;
                    
                    if string.lower(animName):find("load") then
                        track.Priority = Enum.AnimationPriority.Action3;
                    end
                    if animName == "FocusCore" then
                        track.Priority = Enum.AnimationPriority.Action3;
                    end
                end
                if animName == "Sprint" then
                    track.Priority = Enum.AnimationPriority.Action3;
                end

                if prefabs then
                    local primaryToolModel: Model = prefabs[1];

                    track:GetMarkerReachedSignal("PlaySound"):Connect(function(audioId)
                        modAudio.Preload(audioId, 2);
                        local sound = modAudio.PlayReplicated(audioId, primaryToolModel.PrimaryPart);
                        if sound then
                            sound:SetAttribute("WeaponAudio", true);
                        end
                    end)

                    track:GetMarkerReachedSignal("SetTransparency"):Connect(function(paramString)
                        local args = string.split(tostring(paramString), ";");
			
                        local toolModel = primaryToolModel;
                        local prefabNamePrefix = args[1];
                        for a=1, #prefabs do 
                            if prefabs[a].Name:sub(1,#prefabNamePrefix) == prefabNamePrefix then 
                                toolModel = prefabs[a]; 
                                break; 
                            end;
                        end
            
                        local partObj = args[2] and toolModel and toolModel:FindFirstChild(args[2]);
                        local transparencyValue = partObj and args[3];
            
                        if transparencyValue == nil then return end;
            
                        local function setTransparency(obj)
                            if obj:IsA("BasePart") then
                                obj.Transparency = obj:GetAttribute("CustomTransparency") or transparencyValue;
            
                                for _, child in pairs(obj:GetChildren()) do
                                    if child:IsA("Decal") or child:IsA("Texture") then
                                        child.Transparency = transparencyValue;
                                    end
                                end
                                
                            elseif obj:IsA("Model") then
                                for _, child in pairs(obj:GetChildren()) do
                                    setTransparency(child);
                                end
                                
                            end
                        end
                        
                        setTransparency(partObj);
                    end)

                    track:GetMarkerReachedSignal("CloneDebris"):Connect(function(paramString)
                        local args = string.split(tostring(paramString), ";");
			
                        local toolModel = primaryToolModel;
                        local prefabNamePrefix = args[1];
                        for a=1, #prefabs do 
                            if prefabs[a].Name:sub(1,#prefabNamePrefix) == prefabNamePrefix then 
                                toolModel = prefabs[a]; 
                                break; 
                            end;
                        end

                        local partObj = args[2] and toolModel and toolModel:FindFirstChild(args[2]);
                        
                        if partObj then
                            local new = partObj:Clone();
                            
                            if partObj:IsA("Model") then
                                new:PivotTo(partObj:GetPivot());
                                for _, obj in pairs(new:GetChildren()) do
                                    if not obj:IsA("BasePart") then continue end;
                                    obj.CanCollide = true;
                                    obj.Transparency = 0;
                                end
                                
                            elseif partObj:IsA("BasePart") then
                                new.Transparency = 0;
                                new.CFrame = partObj.CFrame;
                                new.CanCollide = true;
                                
                            end
                            
                            
                            new.Parent = workspace.Debris;
                            new:BreakJoints();
                            game.Debris:AddItem(new, 20);
                        end
                    end)

                    track:GetMarkerReachedSignal("PlayParticle"):Connect(function(paramString)
                        local args = string.split(tostring(paramString), ";");

                        local particleObj = primaryToolModel:FindFirstChild(args[1], true);
                        if particleObj == nil then return end;

                        local delaySec = tonumber(args[2]) or 0.1;

                        if not particleObj:IsA("Attachment") then return end;
                        for _, particle in pairs(particleObj:GetChildren()) do
                            particle.Enabled = true;
                            task.delay(delaySec, function()
                                particle.Enabled = false;
                            end)
                        end
                    end)
                end

                track:GetPropertyChangedSignal("IsPlaying"):Connect(function()
                    local isPlaying = track.IsPlaying;

                    if isPlaying then
                        self.TrackEvent:Fire("Play", animName, stateName);
                    else
                        self.TrackEvent:Fire("Stop", animName, stateName);
                    end
                    
                end)

                table.insert(animPacket[stateName], track);
            end
        end

        self.Anims[animName] = animPacket;
	end;

    if setDefaultState then
        self:SetState(setDefaultState);
    end
end

function ToolAnimator:StopAll()
    for key, animPacket in pairs(self.Anims) do
        for state, tracks: {AnimationTrack} in pairs(animPacket) do
            for a=1, #tracks do
                tracks[a]:Stop();
            end
        end
    end
end

function ToolAnimator:SetState(state)
    self.State = state or "";
end

function ToolAnimator:GetTracks(animKey)
    local animPacket = self.Anims[animKey];
    if animPacket == nil then
        Debugger:Warn(`Missing tool animation ({animKey})`);
        return;
    end

    local availableTracks = animPacket[self.State];
    if availableTracks == nil then
        return; 
    end

    return availableTracks;
end

-- get 
function ToolAnimator:GetPlaying(animName): AnimationTrack?
    local animPacket = self.Anims[animName];
    if animPacket == nil then
        return;
    end

    local availableTracks = animPacket[self.State];
    if availableTracks == nil then
        return;
    end

    for a=1, #availableTracks do
        if availableTracks[a].IsPlaying then
            return availableTracks[a];
        end
    end

    return;
end

function ToolAnimator:GetKeysPlaying(animKeys)
    local playingTracks = {};

    for a=1, #animKeys do
        local key = animKeys[a];
        local track = self:GetPlaying(key);
        if track then
            playingTracks[key] = track;
        end
    end

    return playingTracks;
end

function ToolAnimator:Play(animKey, param): AnimationTrack
    param = param or {};

    -- optional params
    local fadeTime = param.FadeTime or nil;
    local playSpeed = param.PlaySpeed or nil;
    local playLength = param.PlayLength or nil;
    local playWeight = param.PlayWeight or nil;

    local activeState = self.State;

    local animPacket = self.Anims[animKey];
    if animPacket == nil then
        Debugger:Warn(`Missing tool animation ({animKey})`);
        return placeholderTrack;
    end

    local availableTracks = animPacket[activeState];
    if availableTracks == nil then
        Debugger:Warn(`Missing tool animation tracks ({animKey}:{activeState})`);
        return placeholderTrack;
    end

    local selectedTrack: AnimationTrack;
    for state, tracks: {AnimationTrack} in pairs(animPacket) do
        if state == activeState then
            selectedTrack = tracks[math.random(1, #tracks)];
            selectedTrack:Play(fadeTime);

            if playSpeed then
                selectedTrack:AdjustSpeed(playSpeed);
            elseif playLength and selectedTrack.Length > 0 then
                selectedTrack:AdjustSpeed(selectedTrack.Length/playLength);
            end

            if playWeight then
                selectedTrack:AdjustWeight(playWeight);
            end

            for a=1, #tracks do
                if tracks[a] ~= selectedTrack then
                    tracks[a]:Stop();
                end
            end

        else
            for a=1, #tracks do
                tracks[a]:Stop();
            end

        end
    end

    return selectedTrack or placeholderTrack;
end

function ToolAnimator:Stop(animKey, param)
    param = param or {};
    
    -- optional params
    local fadeTime = param.FadeTime or nil;

    local animPacket = self.Anims[animKey];
    if animPacket == nil then
        Debugger:Warn(`Missing tool animation ({animKey})`);
        return;
    end

    for state, tracks: {AnimationTrack} in pairs(animPacket) do
        for a=1, #tracks do
            local track = tracks[a];
            if not track.IsPlaying then continue end;

            if state == self.State then
                track:Stop(fadeTime);
            else
                track:Stop();
            end
        end
    end

end

function ToolAnimator:ConnectMarkerSignal(markerKey, func)
    for animKey, animPacket in pairs(self.Anims) do
        for state, tracks in pairs(animPacket) do
            for a=1, #tracks do
                local track: AnimationTrack = tracks[a];
                
                track:GetMarkerReachedSignal(markerKey):Connect(function(value)
                    func(animKey, track, value);
                end)
            end
        end
    end
end

return ToolAnimator;