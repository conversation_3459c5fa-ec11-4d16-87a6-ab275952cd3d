local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modEventSignal = shared.require(game.ReplicatedStorage.Library.EventSignal);

--== AnimGroup
local AnimGroup = {};
AnimGroup.__index = AnimGroup;

function AnimGroup.new(animController, groupId)
	local self = {
		Id = groupId;
		AnimController = animController;
		CatKeys = {};
	};
	
	setmetatable(self, AnimGroup);
	return self;
end

function AnimGroup:LoadAnimation(animName, animPrefabList)
	local catKey = self.Id..animName;
	self.CatKeys[catKey] = animName;
	
	local trackData = self.AnimController:LoadAnimation(catKey, animPrefabList);
	trackData.GroupId = self.Id;
	
	return trackData;
end

function AnimGroup:StopAll()
	for cat<PERSON>ey, catId in pairs(self.CatKeys) do
		self.AnimController:Stop(catKey);
	end
end

function AnimGroup:Play(animName, paramPacket)
	local catKey = self.Id..animName;
	return self.AnimController:Play(catKey, paramPacket);
end

function AnimGroup:HasAnim(animName)
	local catKey = self.Id..animName;
	return self.AnimController:HasAnim(catKey);
end

function AnimGroup:Stop(animName, paramPacket)
	local catKey = self.Id..animName;
	self.AnimController:Stop(catKey, paramPacket);
end

function AnimGroup:Destroy()
	for key, catId in pairs(self.CatKeys) do
		self.AnimController:UnloadAnimation(key);
	end
end

function AnimGroup:GetTrackData(animName, index)
	local catKey = self.Id..animName;
	return self.AnimController:GetTrackData(catKey, index);
end

function AnimGroup:GetPlaying(animName): AnimationTrack?
	local catKey = self.Id..animName;

	local trackGroup = self.AnimController:GetTrackGroup(catKey);
	if trackGroup == nil then
		return;
	end

    for a=1, #trackGroup do
		local trackData = trackGroup[a];
        if trackData.Track and trackData.Track.IsPlaying then
            return trackData.Track;
        end
    end

	return;
end

function AnimGroup:GetTrackGroup(animName)
	local catKey = self.Id..animName;

	return self.AnimController:GetTrackGroup(catKey);
end

function AnimGroup:GetState()
	return self.AnimController.State;
end

function AnimGroup:SetState(state)
	self.AnimController.State = state;
	self.AnimController:Update();
end

function AnimGroup:HasState(k)
	return table.find(self.AnimController.StateList, k) ~= nil;
end

--==

local AnimationController = {};
AnimationController.__index = AnimationController;

--==
function AnimationController.new(animator: Animator, rig: Model?)
	local self = {
		Name = (rig and rig.Name or animator.Parent and animator.Parent.Parent and animator.Parent.Parent.Name);
		Rig = rig;
		Animator = animator;
		
		State = nil;
		StateList = {};

		LoadedAnim = {};
		Tracks = {};
		Actives = {};
		Timescale = 1;
		
		AnimatorLogic = nil;
		
		OnUpdate = shared.EventSignal.new("OnAnimationControllerUpdate");
		Garbage = modGarbageHandler.new();

		TrackEvent = shared.EventSignal.new("AnimationControllerTrackEvent");
	}
	
	for k, v in pairs(Enum.AnimationPriority:GetEnumItems()) do
		self.Actives[v.Name] = {};
	end
	
	setmetatable(self, AnimationController);
	
	self.Garbage:Tag(animator.Destroying:Connect(function()
		if self.Destroyed then return end;
		self.Destroyed = true;
		
		table.clear(self.LoadedAnim);
		self.Garbage:Destruct();
		table.clear(self :: any);
	end)) 
	
	return self;
end

function AnimationController:NewGroup(groupId)
	local newGroup = AnimGroup.new(self, groupId);
	return newGroup;
end

-- !outline: AnimationController:LoadAnimation(animName, animList, animModule);
function AnimationController:LoadAnimation(animName, animList, animModule)
	if self.Destroyed then return end;
	
	animList = typeof(animList) == "table" and animList or {animList};
	
	local firstTrackData;
	
	for _, animation: Animation in pairs(animList) do
		if self.LoadedAnim[animation] then continue end;
		self.LoadedAnim[animation] = animName;

		local track: AnimationTrack;
		if animation:IsA("Animation") then
			track = self.Animator:LoadAnimation(animation);
			track.Name = animation.Name;
		elseif animation:IsA("AnimationTrack") then
			track = animation;
			track.Name = animName;
		end

		local _priority = animation:GetAttribute("AnimationPriority") or 0;
		local chance = animation:GetAttribute("Chance") or 1;
		
		local trackData = {
			CategoryId = animName;
			
			Name = track.Name;
			Track = track;
			Chance = chance;
			Weight = 1;
			
			LoopStart = nil;
			LoopEnd = nil;
		};

		self.Garbage:Tag(function()
			table.clear(trackData);
			game.Debris:AddItem(track, 0);
		end);

		if track.Looped == false then
			self.Garbage:Tag(track.Stopped:Connect(function()
				local priorityName = trackData.Track.Priority.Name;

				local i = table.find(self.Actives[priorityName], trackData);
				if i == nil then return end;
				
				table.remove(self.Actives[priorityName], i);
				if i <= 1 then
					self:Update();
				end
			end))
		end
		
		track:GetMarkerReachedSignal("Loop"):Connect(function(paramString)
			if paramString == "Start" then
				if trackData.LoopStart == nil then
					trackData.LoopStart = track.TimePosition;
				end
				
			elseif paramString == "End" then
				if trackData.LoopEnd == nil then
					trackData.LoopEnd = track.TimePosition;
				end
				if trackData.LoopStart then
					track.TimePosition = trackData.LoopStart;
				end
				
			end
		end)

		track:GetPropertyChangedSignal("IsPlaying"):Connect(function()
			local isPlaying = track.IsPlaying;
			if isPlaying then
				self.TrackEvent:Fire("Play", animName, trackData.GroupId);
			else
				self.TrackEvent:Fire("Stop", animName, trackData.GroupId);
			end
		end)
		
		if self.Tracks[animName] == nil then
			self.Tracks[animName] = {};
		end
		table.insert(self.Tracks[animName], trackData);
		
		if firstTrackData == nil then
			firstTrackData = trackData;
		end
	end
	
	local _tracksGroup = self.Tracks[animName];
	
	return firstTrackData;
end

-- !outline: AnimationController:SetAnimationMeta(animName, moduleSrc, basePacket)
function AnimationController:SetAnimationMeta(animName, moduleSrc, baseValues)
	if not moduleSrc:IsA("ModuleScript") then return end;
	
	local animationMeta = shared.require(moduleSrc);
	
	self:LoopTracks(animName, function(trackData)
		trackData.Values = baseValues;
		setmetatable(trackData, animationMeta);
	end)
end

function AnimationController:ConnectMarker(animName, markerId, func)
	self:LoopTracks(animName, function(trackData)
		local track: AnimationTrack = trackData.Track;
		track:GetMarkerReachedSignal(markerId):Connect(function(...)
			func(trackData, ...);
		end)
	end)
end

function AnimationController:UnloadAnimation(animName)
	local tracksGroup = self.Tracks[animName];
	if tracksGroup == nil then return end;
	

	if self.Debug == true then
		Debugger:Warn("UnloadAnimation", animName);
	end
	for priorityName, activeList in pairs(self.Actives) do
		for a=1, #tracksGroup do
			local i = table.find(activeList, tracksGroup[a]);
			if i then
				table.remove(activeList, i);
			end
		end
	end
	self.Tracks[animName] = nil;
	
	self:Update();
end

function AnimationController:IsLoaded(animPrefab)
	return self.LoadedAnim[animPrefab] == true;
end

-- !outline :HasAnim(category)
function AnimationController:HasAnim(animName)
	local tracksGroup = self.Tracks[animName];
	return tracksGroup ~= nil;
end

function AnimationController:IsPlaying(animName)
	for priorityName, activeList in pairs(self.Actives) do
		local activeTrack = activeList[1];
		if activeTrack and activeTrack.CategoryId == animName then
			return true;
		end
	end
	return false;
end

function AnimationController:Play(animName, paramPacket)
	if self.Destroyed then return end;
	paramPacket = paramPacket or {};
	
	local tracksGroup = self.Tracks[animName];
	if tracksGroup == nil then
		Debugger:Warn("Play missing animation (",self.Name,")", animName);
		return;
	end;
	
	local animId = paramPacket.AnimId;
	local trackData = nil;
	
	if animId then
		for a=1, #tracksGroup do
			if tracksGroup[a].Id == animId then
				trackData = tracksGroup[a];
				break;
			end
		end
		
	else
		local rollTable = {};
		local totalChance = 0;
		
		for a=1, #tracksGroup do
			if tracksGroup[a].UpdateTrackChance then
				tracksGroup[a]:UpdateTrackChance();
			end
			
			if tracksGroup[a].Chance > 0 then
				totalChance = totalChance + tracksGroup[a].Chance;
				table.insert(rollTable, {
					Value=tracksGroup[a];
					Range=totalChance;
				})
			end
		end

		--trackData = tracksGroup[math.random(1, #tracksGroup)];
		local roll = math.random(0, totalChance*1000)/1000;
		for a=1, #rollTable do
			if rollTable[a].Range >= roll then
				trackData = rollTable[a].Value;
				break;
			end
		end
		
	end
	
	if trackData == nil then
		Debugger:Warn("No trackdata viable to be played. (",self.Name,")", animName);
		return;
	end
	
	local priority = trackData.Track.Priority;
	local activeList = self.Actives[priority.Name];
	
	local alreadyActiveIndex = nil;
	for a=1, #activeList do
		if activeList[a].CategoryId == animName then
			trackData = activeList[a];
			alreadyActiveIndex = a;
			
			break;
		end
	end
	
	local track = trackData.Track;

	if self.Debug then
		Debugger:Warn(":Play(", animName, track, activeList);
	end
	
	if alreadyActiveIndex and alreadyActiveIndex > 1 then
		table.remove(activeList, alreadyActiveIndex);
	end

	if activeList[1] == nil or activeList[1].Track.Looped ~= true then
		activeList[1] = trackData;

	elseif activeList[1] ~= trackData then
		table.insert(activeList, 1, trackData);

	end
	
	if self.Debug then
		Debugger:Warn(":Play)", track, activeList);
	end
	
	trackData.FadeTime = paramPacket.FadeTime;
	trackData.Length = paramPacket.Length or paramPacket.PlayLength;
	trackData.Speed = paramPacket.Speed or paramPacket.PlaySpeed;
	trackData.Weight = paramPacket.Weight or paramPacket.PlayWeight;
	
	if trackData.BindSpeed then
		trackData.Speed = trackData:BindSpeed();
	end
	
	self:Update();
end

function AnimationController:Stop(animName, paramPacket)
	paramPacket = paramPacket or {};
	local tracksGroup = self.Tracks[animName];
	
	if tracksGroup then
		if self.Debug == true then
			Debugger:Warn("StopRequest", animName);
		end
		for a=1, #tracksGroup do
			local track = tracksGroup[a].Track;
			local priorityName = track.Priority.Name;
			
			local activeList = self.Actives[priorityName];
			for b=#activeList, 1, -1 do
				if activeList[b] == tracksGroup[a] then
					table.remove(activeList, b);
					break;
				end
			end
			
			if not track.IsPlaying then continue end;

			tracksGroup[a].Track:Stop(paramPacket.FadeTime);
		end
		
	end
	
	self:Update();
end

function AnimationController:StopAll()
	for k, v in pairs(self.Actives) do
		if self.Debug == true then
			Debugger:Warn("StopAll", k);
		end
		table.clear(self.Actives[k]);
	end
	self:Update();
end

function AnimationController:GetTrackGroup(animName)
	return self.Tracks[animName];
end

-- !outline: :GetTrackData(animName, index)
function AnimationController:GetTrackData(animName, index)
	local tracksList = self:GetTrackGroup(animName);
	if #tracksList <= 0 then return end;

	if index and index > 0 and index < #tracksList then
		return tracksList[index];
	end

	return tracksList[math.random(1, #tracksList)];
end

-- !outline: :LoopTracks(animName, loopFunc)
function AnimationController:LoopTracks(animName, loopFunc)
	local tracksGroup = self:GetTrackGroup(animName);
	if tracksGroup == nil then return end;
	
	for a=1, #tracksGroup do
		loopFunc(tracksGroup[a]);
	end
end

-- !outline: :Update()
function AnimationController:Update()
	local activeTracks = {};
	
	for priorityName, activeList in pairs(self.Actives) do
		for a=1, #activeList do
			local trackData = activeList[a];
			local track = trackData.Track;
			
			if a == 1 then
				table.insert(activeTracks, track);
				
				if not track.IsPlaying then
					if self.Debug == true then
						Debugger:Warn(self.testCount,"Play", track, activeTracks);
					end
					track:Play(trackData.FadeTime);
				end
				
				local animSpeed = 1;
				if trackData.Length then
					animSpeed = track.Length/trackData.Length;

				elseif trackData.Speed then
					animSpeed = trackData.Speed;
	
				end
				
				if trackData.CustomSpeed ~= true then
					track:AdjustSpeed(animSpeed * self.Timescale);
				end

				if trackData.Weight then
					track:AdjustWeight(trackData.Weight);
				end
				
			else
				if track.IsPlaying then
					if self.Debug == true then
						Debugger:Warn("Stop index=a", track, activeTracks);
					end
					track:Stop();
				end
				
			end
		end
	end

	local activeAnimationTracks = self.Animator:GetPlayingAnimationTracks();
	for a=1, #activeAnimationTracks do
		local track = activeAnimationTracks[a];
		if table.find(activeTracks, track) == nil then
			if self.Debug == true then
				Debugger:Warn("Stop Active", track, activeTracks);
			end
			track:Stop();
		end
	end
	
	
	self.OnUpdate:Fire();
end

function AnimationController:SetTimescale(v)
	self.Timescale = v or 1;
	
	self.Animator:SetAttribute("Timescale", self.Timescale);
	
	self:Update();
end

AnimationController.AnimGroup = AnimGroup;
return AnimationController;