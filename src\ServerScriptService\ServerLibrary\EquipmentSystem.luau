local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local TweenService = game:GetService("TweenService");

local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
local modItemPackage = shared.require(game.ReplicatedStorage.Library.ItemPackage);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modItemSkinWear = shared.require(game.ReplicatedStorage.Library.ItemSkinWear);
local modDropAppearance = shared.require(game.ReplicatedStorage.Library.DropAppearance);
local modCustomizationData = shared.require(game.ReplicatedStorage.Library.CustomizationData);
local modLazyLoader = shared.require(game.ReplicatedStorage.Library.LazyLoader);
local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);
local modAnalyticsService = shared.require(game.ServerScriptService.ServerLibrary.AnalyticsService);

local remoteToolInputHandler, remoteEquipmentClass;
local prefabsItems; 

local EquipmentSystem = {};
--==
function EquipmentSystem.onRequire()
	remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler");
	remoteEquipmentClass = modRemotesManager:Get("EquipmentClass");
	prefabsItems = game.ReplicatedStorage.Prefabs.Items;
		
	local function OnPlayerAdded(player: Player)
		player.CharacterRemoving:Connect(function()
			local profile = shared.modProfile:Get(player);
			if profile == nil then Debugger:WarnClient(player, "Could not retrieve profile. Equipment system disabled, please respawn to try again."); return end;
			
			if profile.EquippedTools and profile.EquippedTools.WeaponModels then
				for a=1, #profile.EquippedTools.WeaponModels do
					local weaponModel = profile.EquippedTools.WeaponModels[a];
					game.Debris:AddItem(weaponModel, 0);
				end
			end
		end)

		player.CharacterAdded:Connect(function(character: Model)
			Debugger:Warn(character.Name,"spawned.");
			local profile = shared.modProfile:Get(player);
			if profile == nil then Debugger:WarnClient(player, "Could not retrieve profile. Equipment system disabled, please respawn to try again."); return end;
			
			character.ChildRemoved:Connect(function(toolModel)
				if not toolModel:HasTag("EquipTool") then return end;
				Debugger:StudioWarn("Destroying toolModel", toolModel)
				local siid = toolModel:GetAttribute("StorageItemId");

				if toolModel:GetAttribute("Equipped") == true and profile.EquippedTools.ID == siid and profile.EquippedTools.StorageItem then
					EquipmentSystem.unequipTool(player, {
						Id=siid;
						StorageItem=profile.EquippedTools.StorageItem;
					});
				end
			end)

			local modGearAttachments = shared.require(character:WaitForChild("GearAttachments"));
			
			for itemId, toolHandler in pairs(profile.ToolsCache) do
				local prefabs = {};
				if toolHandler.ToolConfig == nil then
					Debugger:Log(itemId,"tool missing toolconfig");
				end
				local storageItem = toolHandler.StorageItem;
				
				if toolHandler.ToolConfig and toolHandler.ToolConfig.Holster then
					for attachmentName, holsterLib in pairs(toolHandler.ToolConfig.Holster) do
						local prefabName = holsterLib.PrefabName;
						local prefabTool = prefabsItems:FindFirstChild(prefabName);
						if prefabTool == nil then
							Debugger:Warn("Tool prefab for (",itemId,") does not exist for holster!");
							return;
						end;
						
						local cloneTool = prefabTool:Clone();
						local handle = cloneTool:WaitForChild("Handle");
						if handle:CanSetNetworkOwnership() then handle:SetNetworkOwner(player); end
						table.insert(prefabs, cloneTool);
						
						local attachment = character:FindFirstChild(attachmentName, true);
						local motor = modGearAttachments:CreateAttachmentMotor(attachment);
						motor.Name = prefabName.."Holster";

						if holsterLib.C1 then
							motor.C1 = holsterLib.C1;
						end
						if holsterLib.Offset then
							motor.C1 = motor.C1 * holsterLib.Offset;
						end
						
						cloneTool.Parent = character;
						motor:SetAttribute("CanQuery", false);
						modGearAttachments:AttachMotor(cloneTool, motor, attachment.Parent, 2);
						
					end
				end
				profile.ToolsCache.Prefabs = prefabs;

				if storageItem == nil then continue end;
				
				local customizationData = storageItem:GetValues("_Customs");
				local activeSkinId = storageItem:GetValues("ActiveSkin");
				if profile.ItemCustomizationBan == 0 and (customizationData or activeSkinId) then
					task.spawn(function()
						modCustomizationData.LoadCustomization({
							ToolModels = prefabs;

							ItemId = itemId;
							CustomizationData = customizationData;
							SkinId = activeSkinId;
						});
					end)
				end

			end
			
		end)
	end;

	remoteToolInputHandler.OnServerEvent:Connect(function(player, packet)
		packet = modRemotesManager.Uncompress(packet);
		
		local character = player.Character;
		if character == nil then Debugger:Warn("Missing Character"); return end;
		
		local action = packet.Action;
		local siid = packet.Siid;
		
		local playerClass: PlayerClass = shared.modPlayers.get(player);
		local wieldComp: WieldComp = playerClass.WieldComp;

		local profile = shared.modProfile:Get(player);
		local inventory = profile.ActiveInventory;

		local id = siid or profile.EquippedTools.ID;
		local toolModels = profile.EquippedTools.WeaponModels;

		local storageItem = inventory and inventory:Find(id);
		if id == "MockStorageItem" then
			storageItem = profile.MockStorageItem;
		end

		if toolModels == nil or #toolModels <= 0 then return end;

		for a=1, #toolModels do
			if not toolModels[a]:IsDescendantOf(character) then
				Debugger:Warn("Tool is no longer a descendant of player (",player.Name,").");
				return 
			end 
		end;

		if storageItem == nil then Debugger:Warn("StorageItem(",id,") does not exist."); return end;
		local itemId = storageItem.ItemId;

		local toolPackage = modItemPackage.getItemPackage(itemId);
		if toolPackage == nil then Debugger:Warn(`Attempt to find toolPackage for key ({itemId})`); return; end;

		local toolHandler: ToolHandlerInstance? = wieldComp.ToolHandler;
		-- :GetToolHandler(
		-- 	storageItem.ID, 
		-- 	storageItem.ItemId,
		-- 	storageItem,
		-- 	toolModels
		-- );
		if toolHandler == nil or toolHandler.StorageItem == nil or toolHandler.StorageItem.ID ~= storageItem.ID then
			Debugger:Warn(`Invalid toolHandler for {storageItem.ID}`);
			return 
		end;


		toolHandler.CharacterClass = shared.modPlayers.get(player);

		if action == "input" then
			if toolHandler.InputEvent then
				toolHandler.InputEvent(toolHandler, packet);
				
				if modConfigurations.RemoveForceFieldOnWeaponFire then
					local forcefield = character:FindFirstChildWhichIsA("ForceField") or nil;
					if forcefield then forcefield:Destroy() end;
				end
			end
			
		elseif action == "action" then
			if toolHandler.ActionEvent then
				toolHandler:ActionEvent(packet);
			end
			
		elseif action == "call" and packet.CallFuncName then
			local callFuncName = packet.CallFuncName;
			if toolHandler.ToolPackage[callFuncName] then
				toolHandler.ToolPackage[callFuncName](toolHandler, packet);
			end
			
		end

	end)

	shared.modEngineCore:ConnectOnPlayerAdded(script, OnPlayerAdded);
end

function EquipmentSystem.unequipTool(player, returnPacket)
	local playerClass: PlayerClass = shared.modPlayers.get(player);

    local profile = shared.modProfile:Find(player.Name);
	local lastStorageItem = profile and profile.EquippedTools and profile.EquippedTools.StorageItem;
	local lastSiid = profile and profile.EquippedTools and profile.EquippedTools.ID;
	
	returnPacket = returnPacket or {};
	
	if lastSiid == nil then return end;
	local itemId = lastStorageItem.ItemId;
	
	returnPacket.Unequip = {
		Id=lastSiid;
		StorageItem=lastStorageItem;
	}
	
	local toolPackage = modItemPackage.getItemPackage(itemId);
	if toolPackage == nil then Debugger:Warn(`Attempt to find toolPackage for key ({itemId})`); return; end;

	local handler = profile.ToolsCache[lastSiid];
	if player == nil or player.Character == nil then return returnPacket; end;
	local modGearAttachments = shared.require(player.Character:FindFirstChild("GearAttachments"));
	
	local toolHandler: ToolHandlerInstance = playerClass.WieldComp:GetToolHandler(
		lastSiid,
		lastStorageItem.ItemId,
		lastStorageItem
	);
	
	local equipmentClass = toolHandler.EquipmentClass;
	equipmentClass:SetEnabled(false);
	remoteEquipmentClass:FireClient(player, "setenable", lastSiid, false);
	shared.modEventService:ServerInvoke("Players_BindWieldEvent", {ReplicateTo={player}}, "Unequip", toolHandler);
	
	if profile.EquippedTools.WeaponModels then
		local preexistingMotors = {};
		local holsterData = handler and handler.ToolPackage.Holster;

		for a=1, #profile.EquippedTools.ToolWelds do
			local motor = profile.EquippedTools.ToolWelds[a];
			if holsterData and motor.Part1 then
				modGearAttachments:Detach(motor.Part1.Parent, motor.Name);
			end
			table.insert(preexistingMotors, motor);
			game.Debris:AddItem(motor, 1);
		end
		
		for a=1, #profile.EquippedTools.WeaponModels do
			local weaponModel = profile.EquippedTools.WeaponModels[a];

			local tween: Tween = TweenService:Create(weaponModel, TweenInfo.new(0.6), {});
			tween.Completed:Connect(function(status)
				if status ~= Enum.PlaybackState.Completed then return end;
				weaponModel:SetAttribute("Equipped", false);
			end)
			tween:Play();
			
			if weaponModel.Parent ~= nil and (handler == nil or holsterData == nil) then
				for _, obj in pairs(weaponModel:GetDescendants()) do
					if obj:IsA("RopeConstraint") then obj.Visible = false; end;
				end
				weaponModel:SetAttribute("StorageItemId", nil);
				game.Debris:AddItem(weaponModel, returnPacket.ToolSwap and 0 or 0.6);
				modGearAttachments:DestroyAttachments(weaponModel);
				Debugger:StudioWarn("Destroy toolModel");
				
			elseif holsterData then
				for attachmentName, holsterLib in pairs(holsterData) do
					local prefabName = holsterLib.PrefabName;
					if prefabName == weaponModel.Name and player.Character:FindFirstChild(prefabName.."Holster", true) == nil then
						local attachment = player.Character:FindFirstChild(attachmentName, true);
						local _motorName = attachmentName.."Motor";
						
						local motor = modGearAttachments:CreateAttachmentMotor(attachment);
						if motor then
							local baseMotorC1 = motor.C1;
							if holsterLib.C1 then
								motor.C1 = holsterLib.C1;
								baseMotorC1 = motor.C1;
							end
							if RunService:IsStudio() then
								if holsterLib.Offset then
									motor:SetAttribute("Offset", holsterLib.Offset);
								end
								motor:GetAttributeChangedSignal("Offset"):Connect(function()
									local offset = motor:GetAttribute("Offset");
									motor.C1 = baseMotorC1 * offset;
									Debugger:Warn(motor.Name,"Offset updated");
								end)
							end
							if holsterLib.Offset then
								motor.C1 = baseMotorC1 * holsterLib.Offset;
							end
							motor.Name = prefabName.."Holster";
							motor:SetAttribute("CanQuery", false);
							
							modGearAttachments:AttachMotor(weaponModel, motor, attachment.Parent, 2);
						end
					end
				end
				
				for _, motor in pairs(preexistingMotors) do
					game.Debris:AddItem(motor, 0);
				end
				
			end
		end
		
	end
	
	local inventory = profile.ActiveInventory;
	local storageItem = inventory and inventory.Find and inventory:Find(lastSiid);
	if storageItem then
		storageItem:DeleteValues("IsEquipped");
		storageItem:Sync({"IsEquipped"});
	end
	
	if profile.EquippedTools.Tick and profile.EquippedTools.ItemId then
		pcall(function()
			local playerSave = profile:GetActiveSave();
			local playerLevel = playerSave:GetStat("Level") or 0;
			
			local duration = math.floor(tick()-profile.EquippedTools.Tick);
			if duration <= 10 then return end;
			
			local key = `Wield_{profile.EquippedTools.ItemId}`;

			if toolPackage.IsWeapon then
				local weaponLevel = storageItem:GetValues("L") or 0;

				if playerLevel >= 100 then
					local modsAttached = 0;
					local attachModTracking = {};
					local attachmentStorage = storageItem and playerSave.Storages[storageItem.ID];
					if attachmentStorage then
						for attachId, attachStorageItem in pairs(attachmentStorage.Container) do
							local attachItemId = attachStorageItem.ItemId;

							local existingTrack;
							for a=1, #attachModTracking do
								if attachModTracking[a].ItemId == attachItemId then
									existingTrack=attachModTracking[a];
									break;
								end
							end

							if existingTrack == nil then
								existingTrack = {ItemId=attachItemId; Value=0;};
								table.insert(attachModTracking, existingTrack);
							end
							existingTrack.Value = existingTrack.Value + duration;

							modsAttached = modsAttached +1;
						end

						table.sort(attachModTracking, function(a, b)
							return a.Value > b.Value;
						end);

						for a=1, #attachModTracking do
							local attachModItemId = attachModTracking[a].ItemId;
							local attachModValue = attachModTracking[a].Value;

							modAnalyticsService:LogCustomEvent{
								Player=player;
								EventName=`Attach_{attachModItemId}`;
								Value=attachModValue;
								CustomFields={
									[Enum.AnalyticsCustomFieldKeys.CustomField01.Name] = `Weapon_{profile.EquippedTools.ItemId}`;
									[Enum.AnalyticsCustomFieldKeys.CustomField02.Name] = `WeaponClass_{toolPackage.WeaponClass or "Misc"}`;
									[Enum.AnalyticsCustomFieldKeys.CustomField03.Name] = `PlayerLevel_{math.round(playerLevel/100)*100}`;
								};
							};
						end
					end

					modAnalyticsService:LogCustomEvent{
						Player=player;
						EventName=`Wield_Weapon`;
						Value=duration;
						CustomFields={
							[Enum.AnalyticsCustomFieldKeys.CustomField01.Name] = profile.EquippedTools.ItemId;
							[Enum.AnalyticsCustomFieldKeys.CustomField02.Name] = `WeaponClass_{toolPackage.WeaponClass or "Misc"}`;
							[Enum.AnalyticsCustomFieldKeys.CustomField03.Name] = `PlayerLevel_{math.round(playerLevel/100)*100}`;
						};
					};

				end
			else
				profile.Analytics:LogTime(key, duration);
				
			end
		end)
	end
	
	if lastStorageItem.MockItem then
		profile.ToolsCache[lastStorageItem.ItemId] = nil;
	end
	
	if toolHandler.ServerUnequip then
		toolHandler:ServerUnequip();
	end
	toolHandler.Garbage:Destruct();
	
	modOnGameEvents:Fire("OnToolUnequipped", player, storageItem);

	playerClass.WieldComp.ToolHandler = nil;
	profile.EquippedTools = {};

	task.spawn(function()
		local playerSave = profile:GetActiveSave();
		playerSave.AppearanceData:UpdateClothingModifiers();
	end)
	
	return;
end

function EquipmentSystem.equipTool(player, packet)
	local playerClass: PlayerClass = shared.modPlayers.get(player);
	local healthComp: HealthComp = playerClass.HealthComp;
	local wieldComp: WieldComp = playerClass.WieldComp;
	local statusComp: StatusComp = playerClass.StatusComp;

	local siid = packet.Siid;
	local profile = shared.modProfile:Get(player);

	local equipmentClass: EquipmentClass = wieldComp:GetEquipmentClass(siid);
	
	local returnPacket = {};
	
	local mockEquip = packet.MockEquip == true;
	
	local function equip()
		if player.Character == nil or player.Character.Parent == nil then
			Debugger:Warn("Player(",player.Name,") attempting to equip while parented to nil.");
			return;
		end
		
		local modGearAttachments = shared.require(player.Character:FindFirstChild("GearAttachments"));
		
		if healthComp.IsDead then
			Debugger:Warn("Player(",player.Name,") attempting to equip while healthComp.IsDead == true.");
			return;
		end
		
		local inventory = profile.ActiveInventory;
		local storageItem = inventory and inventory.Find and inventory:Find(siid);
		
		if mockEquip then
			local itemId = packet.ItemId;
			
			if profile.ItemClassesCache["MockStorageItem"] then
				profile.ItemClassesCache["MockStorageItem"] = nil;
			end
			storageItem = profile.MockStorageItem;
			table.clear(storageItem.Values);
			storageItem:SetItemId(itemId);
			storageItem:Sync();
			siid = storageItem.ID;
			
			Debugger:Log("Mock equip",packet, " StorageItem:", storageItem, storageItem.ItemId);
		else
			if modConfigurations.DisableNonMockEquip == true then
				Debugger:Log("Attempt to equip non-mock item.");
				return;
			end
		end
		
		if storageItem == nil then
			warn("Player(",player.Name,") missing item id(",siid,") or inventory(",inventory,").");
			return;
		end
		
		modItemSkinWear.Generate(player, storageItem);

		task.spawn(function()
			-- skinFix;
			local unlockedSkins = storageItem:GetValues("Skins") or {};
			if #unlockedSkins > 1 then
				modTables.CollapseValues(unlockedSkins);
			end
		end)
		
		local itemId = storageItem.ItemId;
		local itemProperties = modItemsLibrary:Find(itemId);
		
		local toolPackage = modItemPackage.getItemPackage(itemId);
		if toolPackage == nil then Debugger:Warn(`Attempt to find toolPackage for key ({itemId})`); return; end;

		if statusComp:GetOrDefault("Wounded") and toolPackage.HandlerType ~= "HealTool" then 
			Debugger:Warn("Player(",player.Name,") attempt to equip when wounded."); 
			return;
		end;
		
		if itemProperties.Equippable and toolPackage then
			local newWelds, newModels = {}, {};
			local weldPacket = {};
			
			local weldsCount = 0;
			for _,_ in pairs(toolPackage.Welds) do
				weldsCount = weldsCount+1;
			end
			if weldsCount == 0 then Debugger:Warn("Tool (",itemId,") does not have any welds"); end;
			
			for weldName, prefabName in pairs(toolPackage.Welds) do
				
				local prefabTool = prefabsItems:FindFirstChild(prefabName);
				if prefabTool == nil then
					Debugger:Warn("Tool prefab for (",itemId,") does not exist!");
					return returnPacket;
				end;
				
				local motor;
				if prefabTool:FindFirstChild("WieldConfig") and prefabTool.WieldConfig:FindFirstChild(weldName) then
					motor = prefabTool.WieldConfig[weldName]:Clone();
					
				elseif toolPackage.Module:FindFirstChild(weldName) then
					motor = toolPackage.Module[weldName]:Clone();
					
				end
				assert(motor, "Missing ToolGrip for "..itemId);

				local toolModelName = prefabName;
				if weldName == "RightToolGrip" then
					toolModelName = "Right"..prefabName;

				elseif weldName == "LeftToolGrip" then
					toolModelName = "Left"..prefabName;

				end
				
				local cloneTool: Model = modGearAttachments:GetAttachedPrefab(toolModelName) or prefabTool:Clone(); -- getExistingPrefab or
				
				cloneTool:AddTag("EquipTool");
				cloneTool:SetAttribute("ItemId", itemId);
				cloneTool:SetAttribute("StorageItemId", siid);
				
				local handle = cloneTool:WaitForChild("Handle");
				if handle:CanSetNetworkOwnership() then handle:SetNetworkOwner(player); end
				cloneTool.Parent = player.Character;

				if profile.Cache.InfAmmo then
					cloneTool:SetAttribute("InfAmmo", profile.Cache.InfAmmo);
				end
				--cloneTool:SetAttribute("Equipped", true);
				local tween: Tween = TweenService:Create(cloneTool, TweenInfo.new(0.1), {});
				tween.Completed:Connect(function(status)
					if status ~= Enum.PlaybackState.Completed then return end;
					cloneTool:SetAttribute("Equipped", true);
				end)
				tween:Play();

				table.insert(newModels, cloneTool);
				
				local dropAppearanceLib = modDropAppearance:Find(itemId);
				if dropAppearanceLib then
					modDropAppearance.ApplyAppearance(dropAppearanceLib, cloneTool);
				end
				
				for _, part in pairs(cloneTool:GetChildren()) do
					if part:IsA("BasePart") then
						part.CollisionGroup = "Tool";
						part.Massless = true;
						
					elseif part:IsA("Humanoid") then
						part.PlatformStand = true;
						
					end
				end
				
				local handPart, toolGrip;
				if weldName == "ToolGrip" or weldName == "RightToolGrip" then
					toolGrip = motor:Clone();
					handPart = player.Character:FindFirstChild("RightHand");
					modGearAttachments:AttachMotor(cloneTool, toolGrip, handPart, 5);
					table.insert(newWelds, toolGrip);
					
				elseif weldName == "LeftToolGrip" then
					
					toolGrip = motor:Clone();
					handPart = player.Character:FindFirstChild("LeftHand");
					modGearAttachments:AttachMotor(cloneTool, toolGrip, handPart, 5);
					table.insert(newWelds, toolGrip);
					
				end
				
				cloneTool.Name = toolModelName;
				cloneTool:SetAttribute("Grip", weldName);
				table.insert(weldPacket, {
					Hand=handPart;
					Weld=toolGrip;
					Prefab=cloneTool;
				})

			end

			local customizationData = storageItem:GetValues("_Customs");
			local activeSkinId = storageItem:GetValues("ActiveSkin");
			if profile.ItemCustomizationBan == 0 and (customizationData or activeSkinId) then
				task.spawn(function()
					modCustomizationData.LoadCustomization({
						ToolModels = newModels;

						ItemId = itemId;
						CustomizationData = customizationData;
						SkinId = activeSkinId;
					});
				end)
			end

			modOnGameEvents:Fire("OnToolEquipped", player, storageItem);
			
			storageItem:SetValues("IsEquipped", true);
			storageItem:Sync({"IsEquipped"});

			local toolHandler: ToolHandlerInstance = playerClass.WieldComp:GetToolHandler(
				siid,
				storageItem.ItemId,
				storageItem,
				newModels
			);
			playerClass.WieldComp.ToolHandler = toolHandler;

			-- Equip setup
			local toolAnimator: ToolAnimator = toolHandler.ToolAnimator;
			toolAnimator:Init(playerClass, itemId);

			equipmentClass:SetEnabled(true);
			remoteEquipmentClass:FireClient(player, "setenable", siid, true);
			
			if toolHandler.Equip then
				toolHandler:Equip();
			end
			if toolHandler.ServerEquip then
				toolHandler:ServerEquip();
			end

			shared.modEventService:ServerInvoke("Players_BindWieldEvent", {ReplicateTo={player}}, "Equip", toolHandler);

			profile.EquippedTools = {
				ItemId=storageItem.ItemId; 
				ID=siid;
				ToolWelds=newWelds; 
				WeaponModels=newModels; 
				Tick=tick(); 
				StorageItem=storageItem;
			};
			
			profile:SyncAuthSeed(false);

			local equipPacket = {
				AuthSeed=profile.Cache.AuthSeed;
				Id=siid;
				Welds=newWelds;
				ItemId=itemId;
				StorageItem=storageItem;
				MockEquip=mockEquip;
				WeldPacket=weldPacket;
			};
			modLazyLoader.EnsureReplication(player, equipPacket);
			returnPacket.Equip = equipPacket;
			
			task.spawn(function()
				local playerSave = profile:GetActiveSave();
				playerSave.AppearanceData:UpdateClothingModifiers();
			end)

		else
			warn("Player(",player.Name,") tried to equip non-tool (",siid,").");
		end
		
		return;
	end
	
	if profile.EquippedTools.ID == nil then
		equip();
		
	else
		local lastId = profile.EquippedTools.ID;
		local lastStorageItem = profile.EquippedTools.StorageItem;
		
		if lastStorageItem then
			local toolHandler: ToolHandlerInstance = playerClass.WieldComp:GetToolHandler(
				lastStorageItem.ID,
				lastStorageItem.ItemId,
				lastStorageItem
			);
			local lastEquipmentClass: EquipmentClass = toolHandler.EquipmentClass;

			if equipmentClass ~= lastEquipmentClass then
				returnPacket.ToolSwap = lastId;
			end
						
			EquipmentSystem.unequipTool(player, returnPacket);

			if equipmentClass ~= lastEquipmentClass then
				equip();
			end
		end

	end
	
	return returnPacket;
end

return EquipmentSystem;