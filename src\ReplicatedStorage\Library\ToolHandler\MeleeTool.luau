local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local UserInputService = game:GetService("UserInputService");
local CollectionService = game:GetService("CollectionService");

local localPlayer = game.Players.LocalPlayer;

local modToolHandler = shared.require(game.ReplicatedStorage.Library.ToolHandler);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modWeaponMechanics = shared.require(game.ReplicatedStorage.Library.WeaponsMechanics);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);
local DamageData = shared.require(game.ReplicatedStorage.Data.DamageData);
local modArcTracing = shared.require(game.ReplicatedStorage.Library.ArcTracing);
local modProjectile = shared.require(game.ReplicatedStorage.Library.Projectile);
local modDamageTag = shared.require(game.ReplicatedStorage.Library.DamageTag);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

local modRadialImage = shared.require(game.ReplicatedStorage.Library.UI.RadialImage);

local remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler");

local LOAD_RADIAL_CONFIG = '{"version":1,"size":128,"count":60,"columns":8,"rows":8,"images":["rbxassetid://**********"]}';

local meleeColliderOverlapParams = OverlapParams.new();
meleeColliderOverlapParams.MaxParts = 16;

local THROW_PRIMED_THRESHOLD = 0.2;

local toolHandler = modToolHandler.new();
--==

function toolHandler.Equip(handler: ToolHandlerInstance)
	local equipmentClass: EquipmentClass = handler.EquipmentClass;
	local configurations: ConfigVariable = equipmentClass.Configurations;
	local properties: PropertiesVariable<{}> = equipmentClass.Properties;

	properties.Disabled = false;
	properties.Attacking = false;

	handler:LoadWieldConfig();
end

if RunService:IsClient() then -- MARK: Client
	local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);

	local BaseStats = {
		MaxStamina = 100;
		DeficiencyStart = 0.2;
		RecoveryRecoveryRate = 25;
		RecoveryRecoveryDelay = 5;
	};
	local Stats = setmetatable({Stamina = 0; SubStamina = 0;}, {__index=BaseStats;});

	function toolHandler.Init(handler: ToolHandlerInstance)
		if Stats.initStamina then return end;
		Stats.initStamina = true;

		task.spawn(function()
			local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);

			while true do
				local delta = task.wait(0.1);

				Stats.RecoveryRecoveryRate = nil;
				Stats.RecoveryRecoveryDelay = nil;

				if playerClass.Properties.isBloxyRush then
					Stats.RecoveryRecoveryRate = 30;
					Stats.RecoveryRecoveryDelay = 1;
				end

				local additionalStamina = playerClass.Configurations.AdditionalStamina;
				if modConfigurations.DisableGearMods then
					additionalStamina = nil;
				end

				if additionalStamina then
					Stats.MaxStamina = BaseStats.MaxStamina + additionalStamina;
				else
					Stats.MaxStamina = nil;
				end

				local rate = delta * Stats.RecoveryRecoveryRate;
				if Stats.LastDrain == nil or (tick()-Stats.LastDrain) > Stats.RecoveryRecoveryDelay then
					Stats.Stamina = math.clamp(Stats.Stamina + rate, -BaseStats.RecoveryRecoveryRate, Stats.MaxStamina);
					Stats.SubStamina = 0;
				end

			end
		end)
	end

	function toolHandler.initInterface()
		local interface: Interface = modClientGuis.ActiveInterface;
		if interface == nil then return; end;

		local meleeWindow: InterfaceWindow = modClientGuis.getWindow("MeleeWindow");
		if meleeWindow ~= nil then return meleeWindow; end

		local meleeFrame = script:WaitForChild("MeleeWindow"):Clone();
		meleeFrame.Parent = interface.ScreenGui;

		meleeWindow = interface:NewWindow("MeleeWindow", meleeFrame);
		meleeWindow.IgnoreHideAll = true;
		meleeWindow.ReleaseMouse = false;
		meleeWindow.Layers = {"CharacterHud"; "CompactHidden"};
		meleeWindow.UseTween = false;

		local crosshairFrame = meleeFrame:WaitForChild("CrosshairFrame");
		local radialBarImage = crosshairFrame:WaitForChild("RadialBar");
		local radialBar = modRadialImage.new(LOAD_RADIAL_CONFIG, radialBarImage);
		
		local binds = meleeWindow.Binds;

		function binds.MeleeRender(config, props)
			if tick() <= props.RadialTick then
				radialBarImage.Visible=true;

				local alpha = 1-math.clamp((props.RadialTick-tick())/props.RadialDuration, 0, 1);
				radialBar:UpdateLabel(alpha);
			else
				radialBarImage.Visible=false;
			end
		end

		return meleeWindow;
	end

	function toolHandler.ClientEquip(handler: ToolHandlerInstance)
		local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
		
		local modCharacter = modData:GetModCharacter();
		local mouseProperties = modCharacter.MouseProperties;
		local characterProperties = modCharacter.CharacterProperties;

		local character: Model = playerClass.Character;
		local playerHead: BasePart = playerClass.Head;
		local rootPart: BasePart = playerClass.RootPart;

		local storageItem = handler.StorageItem;
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;

		local siid: string = storageItem.ID;

		local toolPackage = handler.ToolPackage;
		local animations = toolPackage.Animations;
		local audio = toolPackage.Audio;

		local itemLib = storageItem.Library;

		local configurations: ConfigVariable = equipmentClass.Configurations;
		local properties: PropertiesVariable<{}> = equipmentClass.Properties;

		local primaryToolPrefab: Model = handler.MainToolModel;
		local primaryHandle = primaryToolPrefab:WaitForChild("Handle");
		local meleeCollider: BasePart = primaryToolPrefab:WaitForChild("Collider") :: BasePart;

		local pointRaycast = RaycastParams.new();
		pointRaycast.FilterType = Enum.RaycastFilterType.Include;
		pointRaycast.IgnoreWater = true;
		pointRaycast.CollisionGroup = "Raycast";
		pointRaycast.FilterDescendantsInstances = {workspace.Environment; workspace.Terrain; workspace.Entity; workspace:FindFirstChild("Characters")};
		
		local mechanicsBarElement: InterfaceElement = modClientGuis.getElement("MechanicsBar");
		--MARK: Init
		properties.RadialTick = tick()-1;
		properties.RadialDuration = 0.1;

		--== MARK: Preload
		for k, audioData in pairs(toolPackage.Audio) do
			modAudio.Preload(audioData.Id);
		end

		local meleeMode = configurations.Mode or "Swing";

		local comboCounter = 0;
		local comboTick = nil;

		local unequiped = false;
		local equipped = false;

		local initThrow = false;
		local throwChargeTick;
		local throwChargeValue = 0;

		characterProperties.HideCrosshair = false;
		if configurations.UseViewmodel == false then
			characterProperties.UseViewModel = false;
		end


		local meleeHitCache = {};
		handler.Garbage:Tag(meleeHitCache);

		local instanceCache: {[any]: any} = {};
		handler.Garbage:Tag(instanceCache);

		--MARK: Melee Window
		local meleeWindow: InterfaceWindow = toolHandler.initInterface();
		local windowBinds = meleeWindow and meleeWindow.Binds or {};
		--==

		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");

		toolAnimator:ConnectMarkerSignal("Event", function(animId, animTrack, value)
			if toolPackage.OnMarkerEvent ~= nil then
				toolPackage.OnMarkerEvent(handler, animId, animTrack, value);
			end
		end);

		handler.Garbage:Tag(toolAnimator.TrackEvent:Connect(function(action: string, animName: string, stateName: string)
			if animName == "Core" then return end;
			if action == "Play" then
				properties.WaistYStrength = animations[animName].WaistStrength or 1;
			elseif action == "Stop" then
				properties.WaistYStrength = 0;
			end
		end));


		local victims = {};
		local function onColliderTouch(hitPart)
			local model = hitPart.Parent;
			if model == nil then return end;

			local healthComp: HealthComp? = modHealthComponent.getByModel(model);
			if healthComp then
				modClientGuis.fireEvent("TryHookEntity", healthComp:GetModel());

				local key = healthComp:GetModel();
				if victims[key] == nil then
					victims[key] = true;

					if modConfigurations.PvpMode then
						return;
					end

					modWeaponMechanics.ImpactSound{
						Enemy = true;
						BasePart = hitPart;
						Point = meleeCollider.Position;
						HideMolten = true;
					};
				end

			else
				if meleeHitCache[model] == nil then
					meleeHitCache[model] = true;

					task.delay(properties.AttRate or 0.5, function()
						meleeHitCache[model] = nil;
					end)

					modWeaponMechanics.ImpactSound{
						BasePart = hitPart;
						Point = meleeCollider.Position;
						HideMolten = true;
					};
				end
			end
		end

		--MARK: MeleePointRay
		toolAnimator:ConnectMarkerSignal("MeleePointRay", function()
			if properties.MeleePointFirstHit then return end;

			local mpOrigin = playerHead.Position;
			local mpDirection = workspace.CurrentCamera.CFrame.LookVector;
			local mpLength = configurations.HitRange/1.75;

			local function onCast(rayHit, rayPoint, rayNormal)
				if rayHit == nil then return end;
				-- local ray = Ray.new(mpOrigin, mpDirection * mpLength);
				-- Debugger:Ray(ray, rayHit, rayPoint, rayNormal);
				properties.MeleePointFirstHit = rayHit;
				
				modWeaponMechanics.ImpactSound{
					BasePart = rayHit;
					Point = rayPoint;
				};

				if toolPackage.BindMeleePointHit then
					local toolInputPacket = toolPackage.BindMeleePointHit(handler, {
						RayHit = rayHit;
						RayPoint = rayPoint;
						RayNormal = rayNormal;
					});
					if toolInputPacket and typeof(toolInputPacket) == "table" and toolInputPacket.Action then
						remoteToolInputHandler:FireServer(modRemotesManager.Compress(toolInputPacket));
					end

				else
					onColliderTouch(rayHit);
				end
			end
			modWeaponMechanics.CastHitscanRay{
				Origin = mpOrigin;
				Direction = mpDirection;
				IncludeList = pointRaycast.FilterDescendantsInstances;
				Range = mpLength;
				OnCastFunc = onCast;
			};
		end)

		local nextAttackTick = tick()-5;
		local lastKeyFireDown = nil;
		local function PrimaryFireRequest(...)
			lastKeyFireDown = tick();
			if configurations.RoleplayStateWindow then
				local roleplayStateWindow = modClientGuis.getWindow(configurations.RoleplayStateWindow);
				if roleplayStateWindow and roleplayStateWindow.Visible then
					return;
				end
			end

			if not characterProperties.CanAction then return end;
			if properties.Disabled then return end;
			if configurations.Throwable and characterProperties.IsFocused then
				repeat
					task.wait();
					if lastKeyFireDown == nil then break; end;
					if initThrow then break; end;
					if not equipmentClass.Enabled then break; end;
				until not characterProperties.IsFocused;
				if initThrow or not equipmentClass.Enabled then
					return;
				end
			end;

			if meleeMode == "Swing" then
				if properties.Attacking then return end;
				if tick()-nextAttackTick < 0 then return end;
				Stats.LastDrain = tick();

				toolAnimator:Stop("Inspect");

				victims = {};

				local rayWhitelist = CollectionService:GetTagged("TargetableEntities") or {};
				table.insert(rayWhitelist, workspace.Environment);
				table.insert(rayWhitelist, workspace.Characters);
				table.insert(rayWhitelist, workspace.Terrain);
				pointRaycast.FilterDescendantsInstances = rayWhitelist;

				properties.MeleePointFirstHit = nil;
				properties.Attacking = true;

				local staminaCost = (configurations.StaminaCost or 10);

				local deficiency = 1;
				local defill = Stats.MaxStamina*Stats.DeficiencyStart;

				if Stats.Stamina < defill then
					local a = 1-math.clamp(Stats.Stamina/defill, 0, 1);
					deficiency = 1 + (configurations.StaminaDeficiencyPenalty or 0.5) * a;
				end
				if Stats.SubStamina > 0 then
					deficiency = deficiency + (Stats.SubStamina/Stats.MaxStamina);
				end

				local function primaryAttack(comboIndex)
					remoteToolInputHandler:FireServer(modRemotesManager.Compress({
						Action = "action";
						ActionIndex = 1;
						ComboIndex = comboIndex;
					}));

					if audio.PrimarySwing then
						modAudio.PlayReplicated(audio.PrimarySwing.Id, primaryHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);
					end

					if comboIndex and animations[`ComboAttack{comboIndex}`] then
						local comboInfo = configurations.Combos[comboIndex];
						local animationId = "ComboAttack"..comboIndex;

						modCharacter.CharacterProperties.SpeedMulti:Set("melee", 0.6, 2);
						modCharacter.UpdateWalkSpeed();

						local track = toolAnimator:Play(animationId, {FadeTime=0;});
						track:AdjustWeight(1,0);
						track:AdjustSpeed(track.Length / comboInfo.AnimationSpeed);

						track.Stopped:Once(function()
							modCharacter.CharacterProperties.SpeedMulti:Remove("melee");
						end)

						return;
					end

					local track = toolAnimator:Play("PrimaryAttack");
					track:AdjustWeight(1, 0);
					if configurations.PrimaryAttackAnimationSpeed then
						track:AdjustSpeed(track.Length / configurations.PrimaryAttackAnimationSpeed);
					end
				end

				if configurations.HeavyAttackSpeed and configurations.Category == "Edged" then
					local charge = 0;
					local maxCharged = false;
					repeat
						charge = charge + RunService.Heartbeat:Wait();

						local track = toolAnimator:GetPlaying("HeavyAttack");
						if charge >= 0.15 and track == nil then
							track = toolAnimator:Play("HeavyAttack", {PlayLength=configurations.HeavyAttackSpeed*2;});
						end
						if maxCharged and track.IsPlaying then
							track.TimePosition = track.Length/2;
							track:AdjustSpeed(0);
						end

						maxCharged = charge >= configurations.HeavyAttackSpeed;

						if not characterProperties.CanAction then break; end;
						if not characterProperties.IsEquipped then break; end;
					until not mouseProperties.Mouse1Down;
					if not characterProperties.IsEquipped then return end;

					if maxCharged then
						staminaCost = staminaCost *2;

						local track = toolAnimator:GetPlaying("HeavyAttack");
						track:AdjustSpeed(1);

						remoteToolInputHandler:FireServer(modRemotesManager.Compress({
							Action = "action";
							ActionIndex = 2;
						}));

						if audio.PrimarySwing then
							modAudio.PlayReplicated(audio.PrimarySwing.Id, primaryHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);
						end

					else
						toolAnimator:Stop("HeavyAttack");
						primaryAttack();
					end

				else
					local comboIndex;
					if configurations.Combos then
						if comboTick == nil then
							comboTick = tick();
						end;
						comboCounter = comboCounter +1;
						local comboInfo = configurations.Combos[comboCounter];
						if comboInfo then
							if tick()-comboTick <= comboInfo.TimeSlot then
								comboIndex = comboCounter;
								if comboInfo.ResetCombo then
									comboCounter = 0;
									comboTick = nil;
								end
							else
								comboCounter = 0;
								comboTick = nil;
							end
						end
					end
					primaryAttack(comboIndex);
				end

				if Stats.Stamina <= 0 then
					local times = math.clamp(math.ceil(Stats.SubStamina/staminaCost), 1, math.huge);
					Stats.SubStamina = Stats.SubStamina + (staminaCost*times);
				end


				local infType = primaryToolPrefab:GetAttribute("InfAmmo");
				if infType ~= nil then
					staminaCost = 0;
				end
				Stats.Stamina = math.clamp(
					Stats.Stamina - staminaCost, 
					-BaseStats.RecoveryRecoveryRate, 
					Stats.MaxStamina
				);


				local attackTime = configurations.PrimaryAttackSpeed;

				local changeRef = {AttackTime=attackTime};
				equipmentClass:ProcessModifiers("OnPrimaryMelee", changeRef);
				attackTime = math.max(changeRef.AttackTime, 0.1);

				nextAttackTick = tick()+attackTime;
				task.wait(attackTime);
				if unequiped then return end;

				local cooldownTime = (attackTime * deficiency) - attackTime;
				task.wait(cooldownTime);
				properties.Attacking = false;

				if playerClass.Configurations.AutoSwing and mouseProperties.Mouse1Down and characterProperties.CanAction then
					PrimaryFireRequest();
				end


			elseif meleeMode == "Auto" then
				if properties.Attacking then return end;
				if tick()-nextAttackTick < 0 then return end;
				if Stats.Stamina <= 0 then return end;

				if instanceCache.CoreLoopAudio then
					instanceCache.CoreLoopAudio.PlaybackSpeed = 2;
					instanceCache.CoreLoopAudio.Volume = 1;
				end

				if instanceCache.SwingAudio then
					instanceCache.SwingAudio:Stop();
				end
				if audio.PrimarySwing then
					instanceCache.SwingAudio = modAudio.PlayReplicated(audio.PrimarySwing.Id, primaryHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);
				end

				toolAnimator:Play("PrimaryAttack", {FadeTime=0.4});
				repeat
					if unequiped then return end;
					remoteToolInputHandler:FireServer(modRemotesManager.Compress({
						Action = "action";
						ActionIndex = 1;
					}));

					Stats.LastDrain = tick();
					toolAnimator:Stop("Inspect");

					victims = {};
					properties.Attacking = true;

					local staminaCost = (configurations.StaminaCost or 10);

					local infType = primaryToolPrefab:GetAttribute("InfAmmo");
					if infType ~= nil then
						staminaCost = 0;
					end
					Stats.Stamina = math.clamp(Stats.Stamina - staminaCost, -BaseStats.RecoveryRecoveryRate, Stats.MaxStamina);

					local attackTime = configurations.PrimaryAttackSpeed;

					local changeRef = {AttackTime=attackTime};
					equipmentClass:ProcessModifiers("OnPrimaryMelee", changeRef);
					attackTime = math.max(changeRef.AttackTime, 0.1);

					nextAttackTick = tick()+attackTime;

					local hitParts = workspace:GetPartsInPart(meleeCollider, meleeColliderOverlapParams);
					for a=1, #hitParts do
						if hitParts[a]:IsDescendantOf(character) then continue end;
						onColliderTouch(hitParts[a]);
					end

					properties.AttRate = attackTime;
					task.wait(attackTime);
					if unequiped then return end;

					if not characterProperties.CanAction then break; end;
				until not mouseProperties.Mouse1Down or Stats.Stamina <= 0;
				if unequiped then return end;

				properties.Attacking = false;

				toolAnimator:Stop("PrimaryAttack", {FadeTime=0.6});

				if instanceCache.CoreLoopAudio and audio.Core then
					instanceCache.CoreLoopAudio.PlaybackSpeed = 1;
					instanceCache.CoreLoopAudio.Volume = audio.Core.Volume;
				end

			end
		end;

		local function InspectRequest()
			if properties.Disabled then return end;
			if not properties.Attacking then
				toolAnimator:Play("Inspect", {FadeTime=0;});
			end
		end

		local throwableBindToolRender;
		if configurations.Throwable then
			local projRaycast = RaycastParams.new();
			projRaycast.FilterType = Enum.RaycastFilterType.Include;
			projRaycast.IgnoreWater = true;
			projRaycast.CollisionGroup = "Raycast";
			projRaycast.FilterDescendantsInstances = {workspace.Environment; workspace.Terrain;};

			local arcTracer = modArcTracing.new();

			-- Use ProjectileConfig if available, otherwise fall back to legacy properties
			local projectileConfig = configurations.ProjectileConfig;
			if projectileConfig then
				arcTracer.Bounce = projectileConfig.Bounce;
				arcTracer.LifeTime = projectileConfig.LifeTime;
				arcTracer.Acceleration = projectileConfig.Acceleration;
				arcTracer.AirSpin = projectileConfig.AirSpin;
			else
				-- Legacy properties
				arcTracer.Bounce = configurations.ProjectileBounce;
				arcTracer.LifeTime = configurations.ProjectileLifeTime;
				arcTracer.Acceleration = configurations.ProjectileAcceleration;
				arcTracer.AirSpin = configurations.ProjectileAirSpin;
			end
			arcTracer.Delta = 1/60;

			table.insert(arcTracer.RayWhitelist, workspace.Entity);
			table.insert(arcTracer.RayWhitelist, workspace:FindFirstChild("Characters"));
			local charactersList = CollectionService:GetTagged("PlayerCharacters");
			if charactersList then
				for a=1, #charactersList do
					if charactersList[a] ~= character then
						table.insert(arcTracer.RayWhitelist, charactersList[a]);
					end
				end
			end


			local function reset()
				throwChargeTick = nil;
				throwChargeValue = 0;
			end

			local function getImpactPoint() -- reasonable throw ranges [25, 80];
				local projectileConfig = configurations.ProjectileConfig;
				
				local rayWhitelist = CollectionService:GetTagged("TargetableEntities") or {};
				table.insert(rayWhitelist, workspace.Environment);
				table.insert(rayWhitelist, workspace.Characters);
				table.insert(rayWhitelist, workspace.Terrain);
				projRaycast.FilterDescendantsInstances = rayWhitelist;

				local velocity = (projectileConfig.Velocity + configurations.VelocityBonus * throwChargeValue);

				local scanPoint = modWeaponMechanics.CastHitscanRay{
					Origin = mouseProperties.Focus.p;
					Direction = mouseProperties.Direction;
					IncludeList = rayWhitelist;
					Range = velocity;
				};

				local newDirection = (scanPoint-playerHead.Position).Unit;
				local distance = (scanPoint-playerHead.Position).Magnitude;

				-- Gets where player can hit.
				-- Get hitscan point from head using direction provided by crosshair hitscan.
				local impactPoint = modWeaponMechanics.CastHitscanRay{
					Origin = playerHead.Position;
					Direction = newDirection;
					IncludeList = rayWhitelist;
					Range = distance;
				};

				return impactPoint;
			end

			local function primaryThrow()
				if storageItem.MockItem ~= true then
					storageItem = modData.GetItemById(storageItem.ID);
				end
				if storageItem == nil then return end;

				local throwStaminaCost = (configurations.ThrowStaminaCost or 0);
				local infType = primaryToolPrefab:GetAttribute("InfAmmo");
				if infType ~= nil then
					throwStaminaCost = 0;
				end
				if Stats.Stamina <= 0 then
					toolAnimator:Stop("Charge", {FadeTime=0;});
					toolAnimator:Play("Throw", {FadeTime=0; PlaySpeed=0.1});

					return
				end;
				Stats.LastDrain = tick();

				properties.CanThrow = false;

				toolAnimator:Stop("Charge", {FadeTime=0;});
				toolAnimator:Play("Throw", {FadeTime=0;});

				if audio.Throw then
					modAudio.PlayReplicated(audio.Throw.Id, primaryHandle);
				end

				Stats.Stamina = math.clamp(Stats.Stamina - throwStaminaCost, -BaseStats.RecoveryRecoveryRate, Stats.MaxStamina);

				for _, obj in pairs(primaryToolPrefab:GetChildren()) do
					if not obj:IsA("BasePart") then continue end;
					if obj.Transparency >= 1 then continue end;

					obj.Transparency = 1;
					task.delay(configurations.ThrowRate or 0.2, function()
						obj.Transparency = 0;
					end)
				end

				local throwCharge = throwChargeValue > 0.05 and throwChargeValue or 0;
				--local impactPoint = getImpactPoint();

				remoteToolInputHandler:FireServer(modRemotesManager.Compress({
					Action = "action";
					ActionIndex = 3;

					ThrowOrigin = primaryHandle.Position;
					--ImpactPoint = impactPoint;
					ThrowDirection = mouseProperties.Direction;
					ThrowCharge = throwCharge;
				}));

				if storageItem.Quantity > 1 and configurations.ConsumeOnThrow ~= true then
					--
				else
					wait(configurations.ThrowRate or 0.2);
					properties.CanThrow = true;
				end
			end
			
			throwableBindToolRender = function()
				local curTick = tick();

				if not characterProperties.IsFocused then
					characterProperties.HideCrosshair = true;

					local track = toolAnimator:GetPlaying("Charge");
					if track then
						track:Stop(0);
						toolAnimator:Play("Throw", {FadeTime=0; PlaySpeed=0.3});
					end

					initThrow = false;
					reset();
					return;
				else
					characterProperties.HideCrosshair = false;
				end

				if characterProperties.CanAction == false 
				or equipmentClass.Enabled == false
				or properties.CanThrow == false then
					reset();
					return;
				end;

				if modKeyBindsHandler:IsKeyDown("KeyFire") then
					if lastKeyFireDown == nil then
						lastKeyFireDown = curTick;
					end
					if throwChargeTick == nil or not properties.CanThrow then
						throwChargeTick = curTick;
					else
						throwChargeValue = math.clamp((curTick-throwChargeTick) / configurations.ChargeDuration, 0.01, 0.99);
						if curTick-throwChargeTick > THROW_PRIMED_THRESHOLD then
							if not initThrow then
								if audio.Charge then
									modAudio.PlayReplicated(audio.Charge.Id, primaryHandle);
								end
							end
							initThrow = true;

							local track = toolAnimator:GetPlaying("Charge");
							if track == nil then
								track = toolAnimator:Play("Charge", {FadeTime=0; PlaySpeed=0;});
							end
							track:AdjustSpeed(0);
							track.TimePosition = track.Length * throwChargeValue;
						end
					end
					characterProperties.Joints.WaistY = configurations.ThrowWaistRotation;

				else
					if initThrow then
						initThrow = false;
						primaryThrow();
					end
					reset();
					lastKeyFireDown = nil;
				end
			end
		end

		-- MARK: ToolRender
		local function meleeRender()
			if not characterProperties.IsEquipped then return end;

			if rootPart:GetAttribute("WaistRotation") then
				characterProperties.Joints.WaistY = math.rad(tonumber(rootPart:GetAttribute("WaistRotation")) or 0);

			else
				if configurations.WaistRotation then
					local waistRot = configurations.WaistRotation;
					if characterProperties.FirstPersonCamera and configurations.FirstPersonWaistOffset then
						waistRot = waistRot + configurations.FirstPersonWaistOffset;
					end
					characterProperties.Joints.WaistY = waistRot;
				end
			end

			if windowBinds.MeleeRender then
				windowBinds.MeleeRender(configurations, properties);
			end

			if throwableBindToolRender then
				throwableBindToolRender();
			end
			
			if mechanicsBarElement then
				mechanicsBarElement.ProgressValue = math.clamp(Stats.Stamina/Stats.MaxStamina, 0, 1);
				mechanicsBarElement.ProgressText = `{math.floor(Stats.Stamina)}/{Stats.MaxStamina}`;
				mechanicsBarElement.ProgressType = "MeleeStamina";
			end
		end
		RunService:BindToRenderStep("ToolRender", Enum.RenderPriority.Camera.Value, meleeRender);


		if meleeMode ~= "Auto" then
			local colliders = {};
			handler.Garbage:Tag(colliders);

			table.insert(colliders, meleeCollider.Touched:Connect(function(hitPart)
				if properties.Attacking == false then return end;
				onColliderTouch(hitPart);
			end))
		end

		local function ItemPromptRequest()
			if not characterProperties.CanAction then return end;
			if characterProperties.ActiveInteract ~= nil and characterProperties.ActiveInteract.CanInteract and characterProperties.ActiveInteract.Reachable then return end;

			if toolPackage.ClientItemPrompt then
				toolPackage.ClientItemPrompt(handler);
			end

			if configurations.RoleplayStateWindow and toolAnimator:HasState("Roleplay") then
				
				toolAnimator:SetState("Roleplay");
				toolAnimator:Play("Core", {FadeTime=0.5;});

				task.spawn(function()
					repeat
						task.wait(0.3);
						local roleplayStateWindow = modClientGuis.getWindow(configurations.RoleplayStateWindow);
						if roleplayStateWindow == nil or roleplayStateWindow.Visible == false then
							break;
						end
					until not equipped;
					toolAnimator:SetState();
					toolAnimator:Play("Core", {FadeTime=0.5;});
				end)

			end
		end

		if audio.Load then
			modAudio.PlayReplicated(audio.Load.Id, primaryHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);
		end

		if audio.Core then
			properties.CoreLoopAudio = modAudio.Play(audio.Core.Id, primaryHandle, true, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);

		end


		local equipTimeReduction = playerClass.Configurations.EquipTimeReduction;
		local equipTime = configurations.EquipLoadTime;
		if equipTimeReduction then
			equipTime = equipTime * math.clamp(1-equipTimeReduction, 0, 1);
		end

		local loadTrack = toolAnimator:Play("Load", {FadeTime=0;});
		if loadTrack.Length > 0 then
			local animSpeed = math.clamp(configurations.EquipLoadTime/equipTime, 0.5, 2);
			loadTrack:AdjustSpeed(animSpeed);
		end

		if toolPackage.OnToolEquip then
			task.defer(toolPackage.OnToolEquip, handler);
		end

		handler.Binds["KeyInteract"] = ItemPromptRequest;
		handler.Binds["KeyFire"] = PrimaryFireRequest;
		handler.Binds["KeyInspect"] = InspectRequest;

		if toolPackage.ClientEquip then
			toolPackage.ClientEquip(handler);
		end
		if toolPackage.ClientItemPrompt then
			if UserInputService.KeyboardEnabled then
				local hintString = configurations.ItemPromptHint or (" to toggle "..itemLib.Name.." menu.")
				hintString = "Press ["..modKeyBindsHandler:ToString("KeyInteract").."]"..hintString;
				modClientGuis.hintWarning(hintString, function(element: InterfaceElement)
					element.TextColor = Color3.fromRGB(255, 255, 255);
				end)
			end

			if UserInputService.TouchEnabled then
				local itemPromptButton = modInterface.TouchControls:WaitForChild("ItemPrompt");
				local touchItemPrompt = itemPromptButton:WaitForChild("Item");

				touchItemPrompt.Image = itemLib.Icon;
				itemPromptButton.Visible = true;

				if modData.ItemPromptConn then modData.ItemPromptConn:Disconnect(); end
				modData.ItemPromptConn = itemPromptButton.MouseButton1Click:Connect(function()
					ItemPromptRequest();
				end)
				handler.Garbage:Tag(modData.ItemPromptConn);
			end
		end
		if toolPackage.SpecialToggleHint then
			if UserInputService.KeyboardEnabled then
				local hintString = `Press [{modKeyBindsHandler:ToString("KeyToggleSpecial")}] {toolPackage.SpecialToggleHint}`;
				modClientGuis.hintWarning(hintString, function(element)
					element.TextColor = Color3.fromRGB(255,255,255);
				end);
			end
			if UserInputService.TouchEnabled then
				local itemPromptButton = modInterface.TouchControls:WaitForChild("ItemPrompt");
				local touchItemPrompt = itemPromptButton:WaitForChild("Item");

				touchItemPrompt.Image = itemLib.Icon;
				itemPromptButton.Visible = true;

				if modData.ItemPromptConn then modData.ItemPromptConn:Disconnect(); end
				modData.ItemPromptConn = itemPromptButton.MouseButton1Click:Connect(function()
					script.Parent.Parent.CharacterInput:Fire("KeyToggleSpecial");
				end)
			end
		end

		task.delay(equipTime, function()
			properties.CanThrow = true;
		end);

		modClientGuis.toggleWindow("MeleeWindow", true);
	end
	
	--MARK: ClientUnequip
	function toolHandler.ClientUnequip(handler: ToolHandlerInstance)
		if modData.ItemPromptConn then
			modData.ItemPromptConn:Disconnect();
		end

		modClientGuis.toggleWindow("MeleeWindow", false);
	end


elseif RunService:IsServer() then -- MARK: Server

	local function processAttack(handler: ToolHandlerInstance, attackToHealthComp: HealthComp, hitPart: BasePart)
		local attackTo: CharacterClass = attackToHealthComp.CompOwner;
		local attackBy: CharacterClass = handler.CharacterClass;

		if not attackToHealthComp:CanTakeDamageFrom(attackBy) then return end;

		local equipmentClass: EquipmentClass = handler.EquipmentClass;

		local configurations = equipmentClass.Configurations;
		local properties = equipmentClass.Properties;

		local damage = configurations.Damage;

		if attackBy.ClassName == "PlayerClass" then
			local playerClass: PlayerClass = attackBy :: PlayerClass;
			local playerRootPart = attackBy.RootPart;

			local distance = (playerRootPart.Position - hitPart.Position).Magnitude;
			local maxDist = math.max(configurations.HitRange, hitPart.Size.Magnitude*1.1)+4;

			if distance > maxDist then
				Debugger:Warn("Illegal hit, distance("..distance.."/"..maxDist..")");
				return
			end

			-- Skill: The Swordsman;
			if playerClass.Properties.theswo then
				local swoBonus = ((playerClass.Properties.theswo.Percent/100)+1);
				damage = damage * swoBonus;
			end
		end

		if properties.AttackType == "Heavy" then
			damage = damage * configurations.HeavyAttackMultiplier;
		end
		
		local toRootPart = attackTo.RootPart;
		if attackTo.ClassName == "NpcClass" then
			local toNpcClass: NpcClass = attackTo :: NpcClass;
			if attackBy.ClassName == "PlayerClass" then -- Player using melee
				local byPlayerClass: PlayerClass = (attackBy :: PlayerClass);

				if handler.ToolPackage.Category == "Blunt" then
					if toNpcClass.KnockbackResistant == nil or toNpcClass.KnockbackResistant == 0 then
						local knockbackStrength = configurations.Knockback or configurations.BaseKnockback;
						if knockbackStrength and damage > 0 then
							local playerRootPart = byPlayerClass.RootPart;
							if toRootPart and playerRootPart then
								toRootPart.Velocity = (playerRootPart.CFrame.LookVector * knockbackStrength) + Vector3.new(0, 40, 0);
							end
						end

						local knockoutDuration = configurations.KnockoutDuration or configurations.BaseKnockoutDuration;
						if knockoutDuration and damage > 0 then
							if attackToHealthComp.CurArmor <= 0 then
								toNpcClass.StatusComp:Apply("meleeKnockout", {
									Expires = workspace:GetServerTimeNow() + knockoutDuration;
									Values = {
										Ragdoll = true;
									}
								});
							end
						end
					end

				elseif handler.ToolPackage.Category == "Edged" then
					if toNpcClass.BleedResistant == nil or toNpcClass.BleedResistant == 0 then
						if attackToHealthComp.CurArmor <= 0 then
							toNpcClass.StatusComp:Apply("meleeBleed", {
								Expires = workspace:GetServerTimeNow() + 5;
								Values = {
									WeaponSiid = handler.StorageItem.ID;
									ApplyBy = attackBy;

									Damage = damage;
									DamagePercent = configurations.BleedDamagePercent or 0.05;
									SlowPercent = configurations.BleedSlowPercent or 0.1;
								};
							});

						end
					end
				end

			elseif attackBy.ClassName == "NpcClass" then -- Npc using melee
				local toHealthComp: HealthComp = attackTo.HealthComp;

				if not toHealthComp:CanTakeDamageFrom(attackBy) then
					damage = 0;
				end
			end

		elseif attackTo.ClassName == "PlayerClass" then -- Player victim;
		end

		if damage == 0 then return end; -- no damage;

		modDamageTag.Tag(attackTo.Character, attackBy.Character, {
			WeaponItemId=(handler.StorageItem and handler.StorageItem.ItemId or nil);
			IsHeadshot=(hitPart.Name == "Head" or hitPart:GetAttribute("IsHead") == true or nil);
		});

		local newDmgData = DamageData.new{
			Damage = damage;
			DamageBy = attackBy;
			ToolHandler = handler;
			ToolStorageItem = handler.StorageItem;
			TargetPart = hitPart;
			DamageCate = DamageData.DamageCategory.Melee;
		};
		attackToHealthComp:TakeDamage(newDmgData);

		if handler.ToolPackage.OnEnemyHit then
			handler.ToolPackage.OnEnemyHit(handler, attackTo, damage);
		end

		if modConfigurations.PvpMode then
			local playedImpactSound = modWeaponMechanics.ImpactSound{
				Enemy = true;
				BasePart = hitPart;
				Point = hitPart.Position;
				HideMolten = true;
				WeaponType = handler.ToolPackage.Category;
			};

			if not playedImpactSound then
				local audio = handler.ToolPackage.Audio;

				local hitSoundId = "MeleeEdgedHit";
				if handler.ToolPackage.Category == "Edged" then
					hitSoundId = "MeleeEdgedHit";
				elseif handler.ToolPackage.Category == "Blunt" then
					hitSoundId = "MeleeBluntHit";
				end

				local snd = modAudio.Play(hitSoundId, hitPart, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);
				snd.PlaybackSpeed = math.random(audio.PrimarySwing.Pitch*10-1, audio.PrimarySwing.Pitch*10+1)/10;
			end
		end
	end

	function toolHandler.ServerEquip(handler: ToolHandlerInstance)
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;

		local toolPackage = handler.ToolPackage;

		local animations = toolPackage.Animations;
		local audio = toolPackage.Audio;

		local configurations = equipmentClass.Configurations;
		local properties = equipmentClass.Properties;

		local meleeTag = Instance.new("BoolValue");
		meleeTag.Name = "MeleeEquipped";
		meleeTag.Parent = handler.CharacterClass.Character;
		handler.Garbage:Tag(meleeTag);

		local victimsList = {};
		properties.VictimsList = victimsList;

		local colliders = {};
		handler.Garbage:Tag(colliders);

		for _, prefab in pairs(handler.Prefabs) do
			local meleeParts = prefab:GetDescendants();
			for a=1, #meleeParts do
				if meleeParts[a].Name == "Collider" and meleeParts[a]:IsA("BasePart") then
					table.insert(colliders, meleeParts[a]);
				end
			end

			for a=1, #colliders do
				handler.Garbage:Tag(colliders[a].Touched:Connect(function(hitPart: BasePart)
					local targetModel = hitPart.Parent;
					if targetModel and targetModel:IsA("Accessory") then
						targetModel = targetModel.Parent;
					end
					
					local healthComp: HealthComp? = modHealthComponent.getByModel(targetModel);
					if healthComp == nil then return end;
					targetModel = healthComp:GetModel();

					local victim = victimsList[targetModel];
					if victim then
						victim.HitTick=tick();
					else
						victimsList[targetModel] = {
							Model=targetModel; 
							HealthComp = healthComp;
							HitPart=hitPart; 
							HitTick=tick();
						};
						victim = victimsList[targetModel];
					end

					if (properties.AttackingTick and tick()-properties.AttackingTick <= configurations.PrimaryAttackSpeed) and victim.Hit ~= true then
						victim.Hit = true;
						processAttack(handler, healthComp, hitPart);
					end

				end));
			end
		end

		properties.Colliders = colliders;

		--MARK: Npc equip
		if handler.CharacterClass.ClassName ~= "NpcClass" then return end;

		local npcClass: NpcClass = handler.CharacterClass :: NpcClass;

		local mainWeaponModel = handler.MainToolModel;

		local cToolWaist = properties.WaistRotation or 0;
		
		npcClass.JointRotations.WaistRot:Set("tool", cToolWaist, 1);
		npcClass.JointRotations.NeckRot:Set("tool", cToolWaist, 1);
		handler.Garbage:Tag(function()
			npcClass.JointRotations.WaistRot:Remove("tool");
			npcClass.JointRotations.NeckRot:Remove("tool");
		end)

		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");

		local cEquipTime = configurations.EquipLoadTime;
		local loadAnimKey = "Load";

		local function playLoad(loadAnimKey: string)
			loadAnimKey = loadAnimKey or "Load";
			if animations[loadAnimKey] then
				toolAnimator:Play(loadAnimKey, {
					PlayLength=math.clamp(cEquipTime, 0.5, 2);
				});
				
				if audio.Load then
					modAudio.Play(audio.Load.Id, mainWeaponModel.PrimaryPart);
				end
			end
		end
		playLoad(loadAnimKey);	

		task.delay(cEquipTime, function()
			properties.CanPrimaryFire = true;
		end);
	end

	function toolHandler.ActionEvent(handler: ToolHandlerInstance, packet)
		local characterClass: CharacterClass = handler.CharacterClass;
		local humanoid: Humanoid = characterClass.Humanoid;

		local actionIndex = packet.ActionIndex;
		local comboIndex = packet.ComboIndex;

		local configurations = handler.EquipmentClass.Configurations;
		local properties = handler.EquipmentClass.Properties;

		if humanoid.Health <= 0 then return end;


		local isBasicAttack = actionIndex == 1 or actionIndex == 2;
		if isBasicAttack then
			local attackTime = configurations.PrimaryAttackSpeed;

			if modConfigurations.DisableGearMods ~= true then
				if characterClass.Configurations.MeleeFury then
					local meleeFuryBonus = 5 * characterClass.Configurations.MeleeFury;
					if meleeFuryBonus > 0 then
						attackTime = attackTime * (1-math.clamp(meleeFuryBonus, 0, 1));
					end
				end
			end

			attackTime = math.max(attackTime, 0.1);

			if properties.AttackingTick and tick()-properties.AttackingTick <= attackTime then return end;

			properties.AttackingTick = tick();
			properties.PrimaryFireTick = tick()-0.5;

			properties.AttackType = actionIndex == 2 and "Heavy" or "Basic";

			local function addVictim(hitPart)
				if characterClass.Character:IsAncestorOf(hitPart) then return end;

				local targetModel = hitPart.Parent;
				local healthComp: HealthComp? = modHealthComponent.getByModel(targetModel);
				if healthComp == nil then return end;
				
				targetModel = healthComp:GetModel();

				local victim = properties.VictimsList[targetModel];
				if victim then
					victim.HitTick = tick();
				else
					properties.VictimsList[targetModel] = {
						Model = targetModel; 
						HealthComp = healthComp; 
						HitPart = hitPart; 
						HitTick = tick();
					};
					victim = properties.VictimsList[targetModel];
				end
			end

			if properties.Colliders then
				for a=1, #properties.Colliders do
					local hitParts = workspace:GetPartsInPart(properties.Colliders[a], meleeColliderOverlapParams);
					for b=1, #hitParts do
						addVictim(hitParts[b]);
					end
				end
			end

			for hitModel, hitInfo in pairs(properties.VictimsList) do
				if hitInfo.HitTick >= properties.PrimaryFireTick then
					hitInfo.Hit = true;
					processAttack(handler, hitInfo.HealthComp, hitInfo.HitPart);
				end
			end

			task.wait(attackTime);

			properties.AttackType = nil;
			properties.AttackingTick = nil;

			for hitModel, hitInfo in pairs(properties.VictimsList) do
				if hitInfo.Hit ~= true then continue end;
				hitInfo.Hit = nil;
			end

		elseif actionIndex == 3 then --MARK: Throw

			local throwOrigin: Vector3 = packet.ThrowOrigin;
			--local impactPoint: Vector3 = packet.ImpactPoint;
			local throwDirection: Vector3 = packet.ThrowDirection;
			local throwCharge: number = packet.ThrowCharge;
			if typeof(throwOrigin) ~= "Vector3" then Debugger:Warn("Origin is not vector3"); return end;
			if typeof(throwDirection) ~= "Vector3" then Debugger:Warn("throwDirection is not vector3"); return end;
			if typeof(throwCharge) ~= "number" then Debugger:Warn("ThrowCharge is not a number"); return end;

			local handle: BasePart? = handler.Prefabs[1].PrimaryPart;
			if handle == nil then return end;

			local distanceFromHandle = (handle.Position - throwOrigin).Magnitude;
			if distanceFromHandle > 32 then Debugger:Warn("Too far from handle."); return end;

			if handler.StorageItem and handler.StorageItem.Quantity <= 0 then return end;

			local projectileConfig = configurations.ProjectileConfig;

			local projectileId = configurations.ProjectileId;
			local projectile: ProjectileInstance = modProjectile.fire(projectileId, {
				OriginCFrame = CFrame.new(throwOrigin);
				ToolHandler = handler;
			});

			throwCharge = math.clamp(throwCharge, 0, 1);

			projectile.ArcTracer.DebugArc = true;
			
			local velocityScalar = (projectileConfig.Velocity + configurations.VelocityBonus * throwCharge);

			-- local travelTime = (impactPoint-throwOrigin).Magnitude/velocityScalar;
			-- local velocity = projectile.ArcTracer:GetVelocityByTime(throwOrigin, impactPoint, travelTime);

			modProjectile.serverSimulate(projectile, {
				Velocity = throwDirection * velocityScalar;
			});

			if characterClass.ClassName == "PlayerClass" then
				local player = (characterClass :: PlayerClass):GetInstance();

				local profile = shared.modProfile:Get(player);
				local inventory = profile.ActiveInventory;

				if configurations.ConsumeOnThrow then
					local storageItem: StorageItem? = handler.StorageItem;
					if storageItem == nil then return end;

					if storageItem.IsFake then
						storageItem.Quantity = (storageItem.Quantity or 1) -1;
						characterClass.WieldComp:Unequip();

					else
						inventory:Remove(storageItem.ID, 1);
						shared.Notify(player, ("1 $Item removed from your Inventory."):gsub("$Item", storageItem.Library.Name), "Negative");

					end
				end
			end

		end
	end

	--MARK: Server Use

	function toolHandler.PrimarySwingRequest(handler: GunToolHandlerInstance)
		local wieldComp: WieldComp = handler.WieldComp;

		local configurations = handler.EquipmentClass.Configurations;
		local properties = handler.EquipmentClass.Properties;
		local toolAnimator = handler.ToolAnimator;

		local audio = handler.ToolPackage.Audio;

		local mainHandle = handler.MainToolModel.PrimaryPart;

		if properties.CanPrimaryFire == false then return end;

		if properties.Attacking then return end;
		properties.Attacking = true;

		table.clear(properties.VictimsList);

		local function primaryAttack()
			properties.AttackingTick = tick();
			modAudio.Play(audio.PrimarySwing.Id, mainHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);

			toolAnimator:Play("PrimaryAttack", {
				FadeTime = 0.05;
				Length = configurations.PrimaryAttackAnimationSpeed;
			});
		end

		if configurations.HeavyAttackSpeed then
			local charge = 0;
			local maxCharged = false;
			repeat
				charge = charge+ RunService.Heartbeat:Wait();
				if charge >= 0.15 then
					toolAnimator:Play("HeavyAttack", {
						FadeTime = 0.05;
						Length = 2/configurations.HeavyAttackSpeed;
					});
				end
				maxCharged = charge >= configurations.HeavyAttackSpeed
			until not wieldComp.Controls.Mouse1Down or maxCharged;

			if maxCharged then
				properties.AttackingTick = tick();
				toolAnimator:Play("HeavyAttack", {
					Speed = 1;
				});
				modAudio.Play(audio.PrimarySwing.Id, mainHandle, nil, audio.PrimarySwing.Pitch, audio.PrimarySwing.Volume);

			else
				toolAnimator:Stop("HeavyAttack");
				primaryAttack();
			end

		else
			primaryAttack();

		end

		task.wait(configurations.PrimaryAttackSpeed);
		properties.Attacking = false;
	end

end

return toolHandler;