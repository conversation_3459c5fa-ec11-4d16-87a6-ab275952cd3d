local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Configuration;
local RunService = game:GetService("RunService");

local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
local modCrateLibrary = shared.require(game.ReplicatedStorage.Library.CrateLibrary);
local modReplicationManager = shared.require(game.ReplicatedStorage.Library.ReplicationManager);
local modDropRateCalculator = shared.require(game.ReplicatedStorage.Library.DropRateCalculator);
local modRewardsLibrary = shared.require(game.ReplicatedStorage.Library.RewardsLibrary);
local modInteractables = shared.require(game.ReplicatedStorage.Library.Interactables);
local modStoragePresetsLibrary = shared.require(game.ReplicatedStorage.Library.StoragePresetsLibrary);


local Crates = {};
Crates.IdCounter = 0;
--==

function Crates.GenerateRewards(id, player, criteria)
	local chosenRewards = {};
	local crateLib = modCrateLibrary.Get(id);
	local rewardsLib = crateLib and modRewardsLibrary:Find(crateLib.RewardsId or id) or nil;
	if rewardsLib then
		chosenRewards = modDropRateCalculator.RollDrop(rewardsLib, player, criteria);
	else
		Debugger:Warn("Missing crate rewardsLib:",id);
	end
	return chosenRewards;
end

-- !outline: Crates.spawn(id, cframe, whitelist, content, visibleToWhitelistOnly)
function Crates.spawn(id, cframe, whitelist, content, visibleToWhitelistOnly)
    local modStorage = shared.require(game.ServerScriptService.ServerLibrary.Storage);

	local crateLib = modCrateLibrary.Get(id);
	if crateLib == nil then return end;
	
	local newPrefab = crateLib.Prefab:Clone();
	newPrefab.PrimaryPart.Anchored = true;
	newPrefab:PivotTo(cframe);
	
	Crates.IdCounter = Crates.IdCounter +1;

	local storagePreset = modStoragePresetsLibrary:Find(crateLib.StoragePresetId);
	local storageConfig = storagePreset.Configuration;

	local storageId = storageConfig.Persistent and crateLib.Id or crateLib.Id.."$r"..Crates.IdCounter;
	local storageName = crateLib.Name;
	
	local newInteractConfig = modInteractables.createInteractable("Storage");
	newInteractConfig.Parent = newPrefab;
	
	local interactable: InteractableInstance = modInteractables.getOrNew(newInteractConfig);

	newPrefab.Name = crateLib.Name;
	
	newInteractConfig:SetAttribute("StorageId", storageId);
	newInteractConfig:SetAttribute("StorageName", storageName);
	newInteractConfig:SetAttribute("StoragePresetId", storagePreset.Id);

	interactable.Values.CrateId = crateLib.Id;
	interactable.Values.StoragePresetId = crateLib.StoragePresetId;
	
	if crateLib.EmptyLabel then interactable.Values.EmptyLabel = crateLib.EmptyLabel end;
	if crateLib.Label then interactable.Values.Label = crateLib.Label; end;
	
	if whitelist then
		for a=1, #whitelist do
			local player: Player = whitelist[a];
			
			interactable:SetUserPermissions(player.Name, "CanInteract", true);
			
			local profile = shared.modProfile:Get(player);
			local gameSave = profile and profile:GetActiveSave();
			if gameSave == nil then continue end;

			local storages = storageConfig.Persistent and gameSave.Storages or profile:GetCacheStorages();
			
			if storages[storageId] == nil then
				local newStorage: Storage = modStorage.new(storageId, storagePreset.Id, player, storageName);
				newStorage.Size = math.clamp(storageConfig.Size or (content and math.ceil(#content/5)*5) or 10, 10, 100);
				newStorage.MaxSize = newStorage.Size;
				newStorage.Values.Physical = newPrefab.PrimaryPart;
				newStorage.Values.ItemSpawn = true;
				storages[storageId] = newStorage;
			end
			
			-- Add content;
			for b=1, #content do
				local rewardInfo = content[b];
				local itemId = rewardInfo.ItemId;
				local quantity = rewardInfo.DropQuantity;
				local itemLib = modItemsLibrary:Find(itemId);
				
				storages[storageId]:Add(itemId, {Quantity=quantity;}, function(event, remains)
					if event ~= "Success" then
						Debugger:Warn("Failed to spawn ("..id..") with its contents.", remains);
					end;
				end)
			end
			
			if storageConfig.Settings then
				for k, v in pairs(storageConfig.Settings) do
					if storages[storageId].Settings[k] ~= nil then
						storages[storageId].Settings[k] = v;
					end
				end
				
				if storageConfig.Settings.DestroyOnEmpty then
					storages[storageId].Garbage:Tag(function()
						game.Debris:AddItem(newPrefab, 1);
					end)
				end
			end

			profile:AddPlayPoints(10, "Source:Reward");
		end
		
		if visibleToWhitelistOnly then
			modReplicationManager.ReplicateIn(whitelist, newPrefab, workspace.Interactables);
		else
			newPrefab.Parent = workspace.Interactables;
		end
	end
	
	local rewardsLib = modRewardsLibrary:Find(id);
	if rewardsLib and rewardsLib.Level then
		interactable.Values.LevelRequired = rewardsLib.Level;
	end 
	
	interactable:Sync();

	shared.modEventService:ServerInvoke("Crates_BindSpawn", {ReplicateTo=whitelist}, newPrefab, interactable, whitelist);
	
	return newPrefab, interactable;
end

function Crates.Create(id, cframe, cratePrefab)
    local modStorage = shared.require(game.ServerScriptService.ServerLibrary.Storage);

	local crateLib = modCrateLibrary.Get(id);
	if crateLib == nil then
		Debugger:Warn(`Attempt to Crates.Create unknown crateId: {id}`);
		return 
	end;

	local newPrefab;
	if cratePrefab == nil then
		if type(crateLib.Prefab) == "table" then
			newPrefab = crateLib.Prefab[math.random(1, #crateLib.Prefab)]:Clone();
		else
			newPrefab = crateLib.Prefab:Clone();
		end
		
		newPrefab.Name = crateLib.Name;
		newPrefab:PivotTo(cframe);
	else
		newPrefab = cratePrefab;
	end

	Crates.IdCounter = Crates.IdCounter +1;

	local storagePresetLib = modStoragePresetsLibrary:Find(crateLib.StoragePresetId);
	if storagePresetLib == nil then
		error(`Missing storage preset for crate: {crateLib.Id} {crateLib.StoragePresetId}`);
	end
	local storageConfig = storagePresetLib.Configuration;
	local storageId = storageConfig.Persistent and crateLib.Id or crateLib.Id.."$r"..Crates.IdCounter;
	local storageName = crateLib.Name;

	local newInteractConfig = modInteractables.createInteractable("Storage");
	newInteractConfig.Parent = newPrefab;
	
	local interactable: InteractableInstance = modInteractables.getOrNew(newInteractConfig);
	interactable:SetPermissions("CanInteract", true);

	newInteractConfig:SetAttribute("StorageId", storageId);
	newInteractConfig:SetAttribute("StorageName", storageName);
	newInteractConfig:SetAttribute("StoragePresetId", storagePresetLib.Id);
	
	interactable.Values.CrateId = crateLib.Id;
	interactable.Values.StoragePresetId = crateLib.StoragePresetId;
	
	
	if crateLib.EmptyLabel then interactable.Values.EmptyLabel = crateLib.EmptyLabel end;
	if crateLib.Label then interactable.Values.Label = crateLib.Label; end;
	
	local newStorage: Storage = modStorage.new(storageId, storagePresetLib.Id, nil, storageName);
	newStorage.Values.Physical = newPrefab.PrimaryPart;

	if storagePresetLib.PublicStorage then
		modStorage.PublicStorages[storageId] = newStorage;
	end

	if cratePrefab == nil then
		if storageConfig.Settings.DestroyOnEmpty then
			newStorage.Garbage:Tag(function(storage)
				game.Debris:AddItem(newPrefab, 0);
			end)
		end
		newPrefab.Parent = workspace.Interactables;
	end

	return newPrefab, interactable, newStorage;
end

function Crates.onRequire()
	
	shared.modCommandsLibrary.bind{
		["crate"]={
			Permission = shared.modCommandsLibrary.PermissionLevel.DevBranch;
			Description = "Spawns a crate.";

			RequiredArgs = 1;
			UsageInfo = "/crate crateId [visibleToListOnly: boolean] [names: {string}]";
			BindDescription = function(cmdStr, args)
                if cmdStr ~= "crate" then return; end;
				local crateId = args and args[1] or "";

				local str = "";
                local listBroke = false;

				local matchIds = {};
				for id, crateLib in pairs(modCrateLibrary.Library) do
					local isMatching = string.match(id, crateId);

					if crateId == nil or #crateId <= 0 or isMatching then
						table.insert(matchIds, id);

                        if #matchIds >= 12 then
                            listBroke = true;
                            break;
                        end
					end
				end

				for a=1, #matchIds do
					str = str..` /crate `..((`{matchIds[a]}\n`):gsub(crateId, `<b>{crateId}</b>`));
				end
                if listBroke then
                    str = str..` /crate ...`;
                end
				return str;
			end;
			Function = function(speaker, args)
				local player = speaker;
				local rootPart = player.Character.PrimaryPart;
				local crateId = args[1];

				local origin = rootPart.CFrame.p + rootPart.CFrame.LookVector*4.5;
				local ray = Ray.new(origin, Vector3.new(0, -16, 0));
				local hit, pos = workspace:FindPartOnRayWithWhitelist(ray, {workspace.Environment});

				if hit == nil then
					shared.Notify(player, "Could not hit ground.", "Negative");
					return;
				end	

			
				local modCrateLibrary = shared.require(game.ReplicatedStorage.Library.CrateLibrary);
				local crateLib = modCrateLibrary.Get(crateId);
				if crateLib == nil then
					shared.Notify(player, "Invalid crate id.", "Negative");
					return;
				end

				local visibleToWhilelistOnly = args[2] == true;

				local playerList = {player};
				if args[3] then
					playerList = {};
					for _, name in pairs(args[3]) do
						if game.Players:FindFirstChild(name) then
							table.insert(playerList, game.Players[name]);
						end
					end
				end

				local rewards = Crates.GenerateRewards(crateId);
				if #rewards > 0 then
					local prefab = Crates.spawn(
						crateId, 
						CFrame.new(pos), 
						playerList, 
						rewards, 
						visibleToWhilelistOnly
					);
					delay(120, function() prefab:Destroy(); end)
				end
				shared.Notify(player, `{crateId} crate spawned.`, "Inform");

				return true;
			end;
		};
	};

end

return Crates;