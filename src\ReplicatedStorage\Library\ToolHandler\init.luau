local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modClientToolAnimator = shared.require(game.ReplicatedStorage.Library.ClientToolAnimator);

local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

--==
local ToolAnimator = {};
ToolAnimator.__index = ToolAnimator;

function ToolAnimator.new()
    local self = {
        _init = false;
    };

    setmetatable(self, ToolAnimator);
    return self;
end

function ToolAnimator:__index(k)
    if k == "TrackEvent" then
        if RunService:IsClient() then
            local toolAnimator = rawget(self, "ClientToolAnimator");
            return toolAnimator.TrackEvent;
        else
            local animGroup = rawget(self, "AnimGroup");
            return animGroup.AnimationController.TrackEvent;
        end
    end

    local v = rawget(self, k);
    if v ~= nil then
        return v;
    end

    return ToolAnimator[k];
end

function ToolAnimator:Init(...)
    if self._init then return end;
    self._init = true;

    local characterClass: CharacterClass, itemId = ...;

    if RunService:IsClient() then
        local playerClass: PlayerClass = characterClass :: PlayerClass;
        self.ClientToolAnimator = modClientToolAnimator.new(playerClass.Humanoid:FindFirstChild("Animator"));

    else
        if characterClass.ClassName == "NpcClass" then
            local npcClass: NpcClass = characterClass :: NpcClass;
            self.AnimGroup = npcClass.AnimationController:NewGroup(itemId);
        end

    end
end

function ToolAnimator:GetPlaying(animName)
    if RunService:IsClient() then
        return self.ClientToolAnimator:GetPlaying(animName);
    else
        return self.AnimGroup:GetPlaying(animName);
    end
end

function ToolAnimator:GetKeysPlaying(animKeys)
    if RunService:IsClient() then
        return self.ClientToolAnimator:GetKeysPlaying(animKeys);
    else
        local playingTracks: {[string]: AnimationTrack} = {};

        for a=1, #animKeys do
            local key = animKeys[a];
            local track = self.AnimGroup:GetPlaying(key);
            if track then
                playingTracks[key] = track;
            end
        end
    
        return playingTracks;
    end
end

function ToolAnimator:Play(animName, param): AnimationTrack
    param = param or {};
    if RunService:IsClient() then
        return self.ClientToolAnimator:Play(animName, param);
    else
        return self.AnimGroup:Play(animName, param);
    end
end

function ToolAnimator:Stop(animName, param)
    param = param or {};
    if RunService:IsClient() then
        return self.ClientToolAnimator:Stop(animName, param);
    else
        return self.AnimGroup:Stop(animName, param);
    end
end

function ToolAnimator:SetState(state)
    if RunService:IsClient() then
        return self.ClientToolAnimator:SetState(state);
    else
        return self.AnimGroup:SetState(state);
    end
end

function ToolAnimator:GetState()
    if RunService:IsClient() then
        return self.ClientToolAnimator.State;
    else
        return self.AnimGroup:GetState();
    end
end

function ToolAnimator:HasState(k)
    if RunService:IsClient() then
        return table.find(self.ClientToolAnimator.StateList, k) ~= nil;
    else
        return self.AnimGroup:HasState(k);
    end
end

function ToolAnimator:LoadAnimations(animationPackage, defaultState, prefabs)
    if RunService:IsClient() then
        return self.ClientToolAnimator:LoadToolAnimations(animationPackage, defaultState, prefabs);

    else
        local mainWeaponModel: Model = prefabs[1];

        for animName, animLib in pairs(animationPackage) do
            local idList = animLib.Id;
            idList = typeof(idList) == "table" and idList or {idList};

            for a=1, #idList do
                if typeof(idList[a]) ~= "number" then continue end;
                local animationId = "rbxassetid://"..idList[a];

                local animationFile = Instance.new("Animation");
                animationFile.Name = `{mainWeaponModel.Name}_{animName}_{a}`;
                animationFile.Parent = mainWeaponModel.PrimaryPart;
                animationFile.AnimationId = animationId;
                
                if animName == "Reload2" then
                    animName = "Reload";
                    animationFile:SetAttribute("Chance", 0.3);
                end

                if a > 1 then
                    animName = `{animName}{a}`;
                end
                
                local trackData = self.AnimGroup:LoadAnimation(animName, animationFile);
                local track: AnimationTrack = trackData.Track;
                
                if animName == "Core" then
                    track.Priority = Enum.AnimationPriority.Movement;
                    
                elseif animName == "Load" or animName == "Idle" then
                    track.Priority = Enum.AnimationPriority.Action3;
                    
                else
                    if animName == "PrimaryFire" then
                        track:AdjustWeight(2, 0.01);
                    elseif animName == "Reload" then
                        track:AdjustWeight(2, 0.05);
                    end
                    track.Priority = Enum.AnimationPriority.Action2;
                end
                
        
                track:GetMarkerReachedSignal("PlaySound"):Connect(function(paramString)
                    modAudio.Play(paramString, mainWeaponModel.PrimaryPart, false);
                end)
                
                track:GetMarkerReachedSignal("SetTransparency"):Connect(function(paramString)
                    local args = string.split(tostring(paramString), ";");
        
                    local toolModel = args[1] == "Left" and mainWeaponModel or mainWeaponModel;
                    local partObj = args[2] and toolModel and toolModel:FindFirstChild(args[2]);
                    local transparencyValue = partObj and args[3];
        
                    if transparencyValue then
                        local function setTransparency(obj)
                            if obj:IsA("BasePart") then
                                obj.Transparency = obj:GetAttribute("CustomTransparency") or transparencyValue;
                                for _, child in pairs(obj:GetChildren()) do
                                    if child:IsA("Decal") or child:IsA("Texture") then
                                        child.Transparency = transparencyValue;
                                    end
                                end
        
                            elseif obj:IsA("Model") then
                                for _, child in pairs(obj:GetChildren()) do
                                    setTransparency(child);
                                end
                            end
                        end
        
                        setTransparency(partObj);
                    end
                end)
            end
		end

        return;
    end
end

function ToolAnimator:ConnectMarkerSignal(markerKey, func)
    if RunService:IsClient() then
        return self.ClientToolAnimator:ConnectMarkerSignal(markerKey, func);

    else
        local trackGroup = self.AnimGroup:GetTrackGroup(markerKey);
        if trackGroup == nil then
            return;
        end

        for a=1, #trackGroup do
            local trackData = trackGroup[a];
            local track: AnimationTrack = trackData.Track;
            if track then
                track:GetMarkerReachedSignal(markerKey):Connect(function(value)
                    func(markerKey, track, value);
                end)
            end
        end

        return;
    end
end

function ToolAnimator:StopAll()
    if RunService:IsClient() then
        return self.ClientToolAnimator:StopAll();

    else
        return self.AnimGroup:StopAll();

    end
end

--==
local ToolHandler = {};
ToolHandler.__index = ToolHandler;
ToolHandler.ClassName = "ToolHandler";

ToolHandler.Handlers = {};

function ToolHandler.loadTypeHandler(moduleScript)
    local handlerType = moduleScript.Name;
    if ToolHandler.Handlers[handlerType] then return ToolHandler.Handlers[handlerType] end;

    local handler = shared.require(moduleScript);

    ToolHandler.Handlers[handlerType] = handler;
    return handler;
end

function ToolHandler.getTypeHandler(handlerType)
    if ToolHandler.Handlers[handlerType] == nil then
        local handlerModule = script:FindFirstChild(handlerType);
        if handlerModule then
            ToolHandler.loadTypeHandler(handlerModule);
        end
    end

    return ToolHandler.Handlers[handlerType];
end

--==
function ToolHandler.new()
    local self = {};

    function self.__index(t, k)
        if k == "MainToolModel" then
            return rawget(t, "Prefabs")[1];
        end

        local v = rawget(t, k);
        if v ~= nil then
            return v;
        end

        return self[k];
    end

    setmetatable(self, ToolHandler);
    return self;
end

function ToolHandler:Unequip()

end

function ToolHandler:Instance(storageItem, toolPackage)
    local new = {
        _init = false;
		StorageItem = storageItem;
		ToolPackage = toolPackage;

        CharacterClass = nil;
        WieldComp = nil;

        Binds = {};
        Prefabs = {};
        ToolGrips = {};

        Garbage = modGarbageHandler.new();
        ToolAnimator = ToolAnimator.new();
    };

    return setmetatable(new, self);
end

function ToolHandler:GetToolGrip(model: Model)
    for _, grip: Motor6D in pairs(self.ToolGrips) do
        if grip.Part1 and model:IsAncestorOf(grip.Part1) then
            return grip;
        end
    end
    return nil;
end

function ToolHandler:LoadWieldConfig()
    local primaryToolPrefab = self.MainToolModel;
    if primaryToolPrefab == nil then return end;

    local wieldConfigModule = primaryToolPrefab:FindFirstChild("WieldConfig");
    if wieldConfigModule == nil then return end;
    
    local equipmentClass: EquipmentClass = self.EquipmentClass;
    if equipmentClass == nil then return end;

    local wieldConfig = shared.require(wieldConfigModule);
    for k, v in pairs(wieldConfig) do
        equipmentClass.Properties[k] = v;
    end
end

function ToolHandler:PlayAudio(audioName: string, parent: Instance, playFunc: (sound: Sound, audioInfo: anydict)->nil)
    local package = self.ToolPackage;
    local audioPackages = package.Audio or {};
    local audioInfo = audioPackages[audioName];

    if audioInfo == nil then return end;
    
    local sound = modAudio.Play(audioInfo.Id, parent);
    if playFunc and sound then
        playFunc(sound, audioInfo);
    end
    
    return sound;
end

function ToolHandler:Destroy()
    for _, toolGrip: Motor6D in pairs(self.ToolGrips) do
        toolGrip:Destroy();
    end
    for _, toolModel: Model in pairs(self.Prefabs) do
        if toolModel.PrimaryPart then
            local handle: BasePart = toolModel.PrimaryPart;
            handle.Massless = false;
            handle.CustomPhysicalProperties = PhysicalProperties.new(4, 0.5, 1, 0.3, 1);
        end
        for _, weaponPart in pairs(toolModel:GetChildren()) do
            if weaponPart:IsA("BasePart") then
                weaponPart.CanCollide = true;
            end
        end
        if game:IsAncestorOf(toolModel) then
            toolModel.Parent = workspace.Debris;
        end
    end
    self.Garbage:Destruct();
end

function ToolHandler.Init()
end

function ToolHandler.ServerEquip()
end

function ToolHandler.ServerUnequip()
end

function ToolHandler.ClientEquip()
end

function ToolHandler.ClientUnequip()
end

return ToolHandler;