local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
shared.waitForIgnition();
--== Configuration;

--== Variables;
local UserInputService = game:GetService("UserInputService");
local RunService = game:GetService("RunService");

local localPlayer = game.Players.LocalPlayer;
local character = script.Parent;
local humanoid: Humanoid = character:Wait<PERSON><PERSON><PERSON>hil<PERSON>("Humanoid");
local animator = humanoid:WaitForChild("Animator");

for a=0, 60 do
	if workspace:IsAncestorOf(animator) then
		break;
	else
		task.wait();
	end
end
if not workspace:IsAncestorOf(animator) then return end;

local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);
local modCharacter = modData:GetModCharacter();

local modGlobalVars = shared.require(game.ReplicatedStorage:WaitForChild("GlobalVariables"));
local modItemPackage = shared.require(game.ReplicatedStorage.Library.ItemPackage);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modToolHandler = shared.require(game.ReplicatedStorage.Library.ToolHandler);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

--== Remotes;
local remoteToolHandler = modRemotesManager:Get("ToolHandler");
local remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler") :: RemoteEvent;

--== Script;
function equip(equipPacket, toolWelds)
	local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);

	local siid = equipPacket and equipPacket.StorageItem and equipPacket.StorageItem.ID;
	
	if siid == nil then Debugger:Warn("Failed to equip item, missing id."); return end;
	if modCharacter.CharacterProperties.IsEquipped then return end;
	
	if equipPacket.MockEquip then
		for k, v in pairs(equipPacket.StorageItem) do
			modData.MockStorageItem[k] = v;
		end
	end
	
	local equipmentStorageItem = modData.GetItemById(siid);
	if equipmentStorageItem == nil then
		Debugger:Warn("Item (",siid,") does not exist in inventory.");
		return;
	end

	local itemId = equipmentStorageItem.ItemId;
	local toolPackage = modItemPackage.getItemPackage(itemId);

	local toolModels = {};
	for a=1, #equipPacket.WeldPacket do
		local weldPacket = equipPacket.WeldPacket[a];
		table.insert(toolModels, weldPacket.Prefab);
	end
	
	
	for a=1, #toolModels do
		local toolModel = toolModels[a] :: Model;
		local handle = toolModel.PrimaryPart;

		if localPlayer.Character:FindFirstChild("EditMode") then
			if handle:FindFirstChild("SightViewModel") == nil then
				local new = Instance.new("Attachment");
				new.Name = "SightViewModel";
				new.Parent = handle;
			end
		end
	end
	
	if modData.Profile and modData.Profile.ItemCustomizationBan and modData.Profile.ItemCustomizationBan >= 1 then
		local modCustomizationData = shared.require(game.ReplicatedStorage.Library.CustomizationData);

		local weaponModelsData = modCustomizationData.LoadWeaponModelsData(itemId, toolModels);
		local partDataList = modCustomizationData.LoadPartDataList(itemId, weaponModelsData);
		modCustomizationData.ClientLoadCustomizations(equipmentStorageItem, partDataList);
	end
	
	local toolHandler = playerClass.WieldComp:GetToolHandler(
		equipmentStorageItem.ID, 
		equipmentStorageItem.ItemId, 
		equipmentStorageItem,
		toolModels
	);
	playerClass.WieldComp.ToolHandler = toolHandler;

	-- Equip setup
    local toolAnimator: ToolAnimator = toolHandler.ToolAnimator;
    toolAnimator:Init(playerClass, itemId);

	local equipmentClass = toolHandler.EquipmentClass;
	equipmentClass:SetEnabled(true);

	if toolHandler then
		if toolHandler.Equip then
			toolHandler:Equip();
		end
		toolHandler:ClientEquip();
		
		if toolPackage.RenderStep then
			RunService:BindToRenderStep("ToolRender", Enum.RenderPriority.Last.Value, function(delta)
				if not workspace:IsAncestorOf(humanoid) then return end;
				toolPackage.RenderStep(toolHandler, delta);
			end);
		end
	end

	modCharacter.CharacterProperties.IsEquipped = true;
	modCharacter.EquippedItem = equipmentStorageItem; -- StorageItem;
	modCharacter.EquippedToolModule = equipmentClass;
	
	if modCharacter.CharacterProperties.ActiveInteract then
		modCharacter.CharacterProperties.ActiveInteract:Trigger();
	end
end

-- MARK: Unequip()
function Unequip(unequipPacket, equipPacket)
	local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);

	local modCharacter = modData:GetModCharacter();
	local characterProperties = modCharacter.CharacterProperties;
	
	local storageItem = unequipPacket and unequipPacket.StorageItem or nil;
	local siid = storageItem and storageItem.ID or unequipPacket.Id;
	
	if siid == nil then Debugger:Warn("Failed to unequip item, missing id."); return end;
	if not modCharacter.CharacterProperties.IsEquipped then return end;
	if modCharacter.EquippedItem == nil or modCharacter.EquippedItem.ID ~= siid then return end;
	
	local equipmentItem = modData.GetItemById(siid) or storageItem;
	
	local wieldComp: WieldComp = playerClass.WieldComp;
	local equipmentClass = wieldComp:GetEquipmentClass(siid);
	
	if storageItem and storageItem.MockItem == true then
		equipmentItem = storageItem;
	end

	local mechanicsBarElement: InterfaceElement = modClientGuis.getElement("MechanicsBar");
	
	equipmentClass:SetEnabled(false);
	playerClass.WieldComp.ToolHandler = nil;

	if equipmentItem then
		local toolHandler: ToolHandlerInstance = playerClass.WieldComp:GetToolHandler(
			equipmentItem.ID, 
			equipmentItem.ItemId, 
			equipmentItem
		);
	
		local itemId = equipmentItem.ItemId;
		local toolPackage = modItemPackage.getItemPackage(itemId);
		
		modCharacter.MouseProperties.Mouse1Down = false;
		modCharacter.MouseProperties.Mouse2Down = false;
		
		if toolHandler then
			RunService:UnbindFromRenderStep("ToolRender");
			toolHandler:ClientUnequip();
			toolHandler.ToolAnimator:StopAll();
			toolHandler.Garbage:Destruct();
			
			characterProperties.Joints.WaistY = 0;
			characterProperties.Joints.WaistZ = 0;
			characterProperties.HideCrosshair = false;
			characterProperties.CustomViewModel = nil;
			characterProperties.AimDownSights = false;
			characterProperties.FieldOfView = nil;
			modCharacter.DevViewModel = nil;
			modCharacter.EquippedTool = nil;

			if equipPacket == nil then
				if toolPackage.Animations["Unequip"] then
					toolHandler.ToolAnimator:Play("Unequip");
				end;
			end
		end

		if mechanicsBarElement then
			mechanicsBarElement.ProgressType = "";
		end
		
		modCharacter.CharacterProperties.UseViewModel = true;
		modCharacter.CharacterProperties.IsEquipped = false;
		modCharacter.CharacterProperties.HideCrosshair = false;
		modCharacter.EquippedItem = nil;
		modCharacter.EquippedToolModule = nil;
		
		if modCharacter.CharacterProperties.ActiveInteract then
			modCharacter.CharacterProperties.ActiveInteract:Trigger();
		end
		
		local touchControlElement: InterfaceElement = modClientGuis.getElement("TouchControlsElement");
		if touchControlElement then
			touchControlElement.ItemPromptIcon = nil;
			touchControlElement.ItemPromptClick = nil;
		end
	end
	
end

function handleTool(returnPacket)
	if returnPacket.Unequip then
		Unequip(returnPacket.Unequip, returnPacket.Equip);
	end
	
	if returnPacket.Equip then
		local authSeed = returnPacket.Equip.AuthSeed;
		
		if modData.Profile.Cache == nil then modData.Profile.Cache = {}; end
		modData.Profile.Cache.AuthSeed = authSeed;
		modData.ShotIdGen = Random.new(authSeed);
		
		equip(returnPacket.Equip, returnPacket.Equip.Welds);
	end

	task.spawn(function()
		local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
		playerClass.WieldComp:RefreshCharacterModifiers();
	end)
end

modData.HandleTool = function(action, paramPacket)
	if action == "local" then
		handleTool(paramPacket);
		return;
	end
	
	coroutine.wrap(function()
		local returnPacket = remoteToolHandler:InvokeServer(action, {
			Siid=paramPacket.Siid; 
			ItemId=paramPacket.ItemId
		});
		handleTool(returnPacket);
	end)()
end

function remoteToolHandler.OnClientInvoke(returnPacket)
	handleTool(returnPacket);
	return;
end

UserInputService.InputBegan:Connect(function(inputObject: InputObject, inputEvent: boolean)
	if  inputObject.UserInputType ~= Enum.UserInputType.Keyboard
	and inputObject.UserInputType ~= Enum.UserInputType.MouseButton1
	and inputObject.UserInputType ~= Enum.UserInputType.MouseButton2
	and inputObject.UserInputType ~= Enum.UserInputType.MouseButton3
	and inputObject.UserInputType ~= Enum.UserInputType.MouseWheel then
		return;
	end;

	if UserInputService:GetFocusedTextBox() ~= nil or inputEvent then return end;
	if not modCharacter.CharacterProperties.IsEquipped then return end;
	
	local storageItem = modCharacter.EquippedItem;
	if storageItem == nil then return end;

	local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
	local toolHandler: ToolHandlerInstance? = playerClass.WieldComp.ToolHandler;
	if toolHandler == nil then return end;

	for keyId, func in pairs(toolHandler.Binds) do
		if modKeyBindsHandler:Match(inputObject, keyId) then
			if keyId == "KeyInteract" then continue end;

			func();
		end
	end

	local toolPackage = toolHandler.ToolPackage;
	if toolPackage.InputEvent then
		local keyIds = modKeyBindsHandler:GetKeyIds(inputObject);
		
		local inputData = {
			InputType="Begin";
			InputObject = inputObject;
			
			KeyIds=keyIds and modGlobalVars.CloneTable(keyIds) or {};
			KeyCode = inputObject.KeyCode;
		};
		
		local submitInput = toolPackage.InputEvent(toolHandler, inputData);
		if submitInput then
			inputData.Action = "input";
			remoteToolInputHandler:FireServer(modRemotesManager.Compress(inputData));
			
		end
	end
end)

UserInputService.InputEnded:Connect(function(inputObject, inputEvent)
	if UserInputService:GetFocusedTextBox() ~= nil then return end;-- or inputEvent
	if not modCharacter.CharacterProperties.IsEquipped then return end;
	
	local storageItem = modCharacter.EquippedItem;
	if storageItem == nil then return end;

	local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
	local toolHandler: ToolHandlerInstance? = playerClass.WieldComp.ToolHandler;
	if toolHandler == nil then return end;

	if toolHandler.ToolPackage.InputEvent then
		local keyIds = modKeyBindsHandler:GetKeyIds(inputObject);
		
		local inputData = {
			InputType="Begin";
			InputObject = inputObject;
			
			KeyIds=keyIds and modGlobalVars.CloneTable(keyIds) or {};
			KeyCode = inputObject.KeyCode;
		};
		
		local submitInput = toolHandler.ToolPackage.InputEvent(toolHandler, inputData);
		if submitInput then
			
			inputData.Action = "input";
			remoteToolInputHandler:FireServer(modRemotesManager.Compress(inputData));
			
		end
	end

end)

character.ChildRemoved:Connect(function(child) 
	local siid = child:GetAttribute("StorageItemId") :: string;
	if siid == nil then return end;
	if modCharacter.EquippedItem == nil then return end;
	if modCharacter.EquippedItem and siid ~= modCharacter.EquippedItem.ID then return end;

	modData.HandleTool("unequip", {Siid=modCharacter.EquippedItem.ID;});
end)

script:WaitForChild("EquipReady").Value = true;