local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Variables;
local Projectile = {
	ProjectileNum=0;
};
local ProjectilePools = {};

local RunService = game:GetService("RunService");
local TweenService = game:GetService("TweenService");
local CollectionService = game:GetService("CollectionService");

local modArcTracing = shared.require(game.ReplicatedStorage.Library.ArcTracing);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modScheduler = shared.require(game.ReplicatedStorage.Library.Scheduler);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

local ProjectileScheduler: Scheduler = modScheduler.new("Projectile");

local remoteSimulateProjectile;
local remoteClientProjectileHit;

local projectilePrefabPoolFolder;
--==
function Projectile.onRequire()
	remoteSimulateProjectile = modRemotesManager:Get("SimulateProjectile");
	remoteClientProjectileHit = modRemotesManager:Get("ClientProjectileHit");
	
	if RunService:IsServer() then
		projectilePrefabPoolFolder = Instance.new("Folder");
		projectilePrefabPoolFolder.Name = "ProjectilePoolPrefabs";
		projectilePrefabPoolFolder.Parent = game.ReplicatedStorage;
			
	elseif RunService:IsClient() then
		local delta = 1/15;
		local tweenInfo = TweenInfo.new(delta+0.01);
		
		remoteSimulateProjectile.OnClientEvent:Connect(function(projectilePrefab)
			if projectilePrefab then
				task.spawn(function()
					local dummyProjectile = projectilePrefab:Clone();
					
					local parentConn;
					parentConn = projectilePrefab:GetPropertyChangedSignal("Parent"):Connect(function()
						if projectilePrefab.Parent == nil then
							Debugger.Expire(dummyProjectile);
							parentConn:Disconnect();
						end
					end)
					
					dummyProjectile.Name = "client"..dummyProjectile.Name;
					dummyProjectile.Parent = workspace.Entities;
					
					local initTransparency = projectilePrefab.Transparency;
					projectilePrefab.Transparency = 1;
					
					for _, obj in pairs(projectilePrefab:GetDescendants()) do
						if obj:IsA("ParticleEmitter") or obj:IsA("Fire") or obj:IsA("Smoke") then
							obj.Enabled = false;
						end
					end
					
					while projectilePrefab:GetAttribute("SyncMotion") and projectilePrefab:IsDescendantOf(workspace) do
						TweenService:Create(dummyProjectile, tweenInfo, {
							Position=projectilePrefab.Position;
							Orientation=projectilePrefab.Orientation;
						}):Play();
						task.wait(delta);
					end
					dummyProjectile.Transparency = 1;
					projectilePrefab.Transparency = initTransparency;
				end);
			end
		end)
	end
end

function Projectile.Fire(projectileId, origin, rotation, spreadedDirection, owner, equipmentClass)
	local projectilePool = ProjectilePools[projectileId];
	if projectilePool == nil then projectilePool = Projectile.Load(projectileId) end;
	
	local projectileObject = Projectile.New(projectileId);
	projectileObject.Configurations = {};

	projectileObject:Init(owner, equipmentClass);
	projectileObject.Id = projectileId;
	projectileObject.Owner = owner;
	
	local projectilePrefab = projectileObject.Prefab;
	CollectionService:AddTag(projectilePrefab, "Projectile");
	
	if projectileObject.Load then 
		projectileObject:Load();
	end;
	
	local arcTracer = modArcTracing.new();
	projectileObject.ArcTracer = arcTracer;
	
	for k, v in pairs(projectileObject.ArcTracerConfig) do
		arcTracer[k] = v;
	end
	
	-- if projectileObject.WeaponModule and projectileObject.WeaponModule.ArcTracerConfig then
	-- 	for k, v in pairs(projectileObject.WeaponModule.ArcTracerConfig) do
	-- 		arcTracer[k] = v;
	-- 	end
	-- end
	
	projectilePrefab.Anchored = false;
	projectilePrefab.CFrame = origin;
	projectilePrefab.Orientation = rotation or Vector3.new();
	if spreadedDirection then
		projectilePrefab.Velocity = spreadedDirection * (arcTracer.Velocity or 50);
	end
	projectilePrefab.Parent = workspace.Entities;
	
	local despawnTime = arcTracer.LifeTime or 10;
	projectilePrefab:SetAttribute("DespawnTime", tick()+despawnTime);
	
	local function despawn()
		local despawnTick = projectilePrefab:GetAttribute("DespawnTime");
		despawnTick = despawnTick and despawnTick-0.1 or nil;
		
		if despawnTick == nil or (tick() >= despawnTick) then
			Debugger.Expire(projectilePrefab);
			if projectileObject.Destroy then
				projectileObject:Destroy();
			end
			
		else
			task.delay(despawnTick-tick(), despawn);
			
		end
	end
	
	task.delay(despawnTime, despawn);
	
	if projectileObject.Activate then 
		task.spawn(function()
			projectileObject:Activate();
		end)
	end;
	
	if RunService:IsClient() then
		Projectile.ProjectileNum = Projectile.ProjectileNum + 1;
		projectileObject.ClientProjNum = Projectile.ProjectileNum;
	end
	return projectileObject;
end


function Projectile.Simulate(projectile, origin, velocity, rayWhitelist)
	local player = projectile.Owner;
	local prefab = projectile.Prefab;
	local arcTracer =  projectile.ArcTracer or modArcTracing.new();
	
	if rayWhitelist then
		arcTracer.RayWhitelist = rayWhitelist;
	end
	
	if arcTracer.IgnoreEntities ~= true then
		table.insert(arcTracer.RayWhitelist, workspace.Entity);
		
		local charactersList = CollectionService:GetTagged("PlayerCharacters");
		if charactersList then 
			for a=1, #charactersList do
				if player == nil or not player:IsA("Player") then
					table.insert(arcTracer.RayWhitelist, charactersList[a]);
					
				elseif player:IsA("Player") and charactersList[a] ~= player.Character then
					table.insert(arcTracer.RayWhitelist, charactersList[a]);
					
				end
			end
		end
	end
	
	local arcPoints;
	
	local projHitConn; 
	if projectile.TrustClientProjectile then
		projHitConn = remoteClientProjectileHit.OnServerEvent:Connect(function(client, projNum, arcPoint)
			for k, v in pairs(arcPoint) do
				if not rawequal(v, v) then
					Debugger:Warn("Dropping illegal remoteClientProjectileHit input.");
					return;
				end
			end
			
			if player == client and projectile.ServerProjNum == projNum then
				arcPoint.Client = true;
				projectile:OnContact(arcPoint);
			end
		end)
	end
	
	arcPoints = arcTracer:GeneratePath(origin, velocity);
	
	prefab:SetAttribute("SyncMotion", true);
	
	arcTracer:FollowPath(arcPoints, prefab, false, function(arcPoint)
		arcPoint:Recast();
		if projectile.BindStepped then
			projectile:BindStepped(arcPoint);
		end
		if (arcPoint.Hit or arcPoint.LastPoint) and projectile.OnContact then
			return projectile:OnContact(arcPoint);
		end
		return;
		
	end, function()
		prefab:SetAttribute("SyncMotion");
		if projectile.OnComplete then
			projectile:OnComplete();
		end
		if projHitConn then projHitConn:Disconnect(); end;
	end)
	
	
	local players = game.Players:GetPlayers();
	for a=1, #players do
		if players[a] ~= player then
			remoteSimulateProjectile:FireClient(players[a], prefab);
		end
	end
end

function Projectile.ClientSimulate(projectile, arcTracer, arcPoints, prefab)
	local prefab = projectile.Prefab;
	
	arcTracer:FollowPath(arcPoints, prefab, true, function(arcPoint)
		if projectile.BindStepped then
			projectile:BindStepped(arcPoint);
		end
		if (arcPoint.Hit or arcPoint.LastPoint) then
			if projectile.OnContact then
				task.spawn(function()
					if arcPoint.Hit == nil then return end;
					
					if modClientGuis then
						modClientGuis.fireEvent("TryHookEntity", arcPoint.Hit.Parent);
					end
				end)
				
				if projectile.TrustClientProjectile then
					remoteClientProjectileHit:FireServer(projectile.ClientProjNum, arcPoint);
				end
				return projectile:OnContact(arcPoint, arcTracer);
			end
		end
	end, function()
		if projectile.OnComplete then
			projectile:OnComplete();
		end
	end);
end

function Projectile.ServerSimulate(projectile, origin, velocity, rayWhitelist)
	local player = projectile.Owner;
	local prefab = projectile.Prefab;
	local arcTracer = projectile.ArcTracer or modArcTracing.new();

	if rayWhitelist then
		arcTracer.RayWhitelist = rayWhitelist;
	end
	
	if arcTracer.IgnoreEntities ~= true then
		table.insert(arcTracer.RayWhitelist, workspace.Entity);
		
		local charactersList = CollectionService:GetTagged("PlayerCharacters");
		if charactersList then 
			for a=1, #charactersList do
				if player == nil or not player:IsA("Player") then
					table.insert(arcTracer.RayWhitelist, charactersList[a]);
					
				elseif player:IsA("Player") and charactersList[a] ~= player.Character then
					table.insert(arcTracer.RayWhitelist, charactersList[a]);
					
				end
			end
		end
		
	end
	
	if arcTracer.AddIncludeTags then
		for _, tagId in pairs(arcTracer.AddIncludeTags) do
			local list = CollectionService:GetTagged(tagId);
			for a=1, #list do
				table.insert(arcTracer.RayWhitelist, list[a]);
			end
		end
	end
	
	local delta = arcTracer.Delta or 1/15;
	task.spawn(function()
		local tweenInfo = TweenInfo.new(delta+0.01, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0);
		prefab.Anchored = true;
		local spinCf = CFrame.new();
		
		--arcTracer.DebugArc = true;
		arcTracer:GeneratePath(origin, velocity, function(arcPoint)
			prefab.CFrame = CFrame.new(arcPoint.Origin, arcPoint.Origin + arcPoint.Direction) * spinCf;
			
			if arcTracer.AirSpin then
				spinCf = spinCf * CFrame.Angles(arcTracer.AirSpin, 0, 0);
			end;
			
			TweenService:Create(prefab, tweenInfo, {Position=arcPoint.Point;}):Play();

			if arcTracer.BindStepped then
				arcTracer.BindStepped(projectile, arcPoint);
			end
			if projectile.BindStepped then
				projectile:BindStepped(arcPoint);
			end
			if (arcPoint.Hit or arcPoint.LastPoint) and projectile.OnContact then
				return projectile:OnContact(arcPoint, arcTracer);
			end
			task.wait(delta);

			return;
		end);
		if projectile.BindArcComplete then
			projectile:BindArcComplete();
		end
	end)
	
end

--MARK: serverSimulate
function Projectile.serverSimulate(projectile: ProjectileInstance, params)
	local projectilePart = projectile.Part;
	local arcTracer: ArcTracer = projectile.ArcTracer;

	local rayWhitelist = params.RayWhitelist;
	if rayWhitelist then
		arcTracer.RayWhitelist = rayWhitelist;
	end
	
	local characterClass: CharacterClass? = projectile.CharacterClass;
	if arcTracer.IgnoreEntities ~= true then
		table.insert(arcTracer.RayWhitelist, workspace.Entity);
		
		local charactersList = CollectionService:GetTagged("PlayerCharacters");
		for a=1, #charactersList do
			if characterClass and characterClass.Character == charactersList[a] then continue end;
			table.insert(arcTracer.RayWhitelist, charactersList[a]);
		end
	end

	if arcTracer.AddTagsToArcTracer then
		for _, tagId in pairs(arcTracer.AddTagsToArcTracer) do
			local list = CollectionService:GetTagged(tagId);
			for a=1, #list do
				table.insert(arcTracer.RayWhitelist, list[a]);
			end
		end
	end

	local delta = arcTracer.Delta or 1/15;
	task.spawn(function()
		local tweenInfo = TweenInfo.new(delta+0.01, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0);
		projectilePart.Anchored = true;
		local spinCf = CFrame.new();
		
		local origin = projectile.OriginCFrame;
		local velocity = params.Velocity;

		local speedMulti = params.SpeedMultiplier;

		if speedMulti then
			arcTracer.SpeedMultiplier = speedMulti;
		else
			arcTracer.SpeedMultiplier = 1;
		end

		--arcTracer.DebugArc = true;
		arcTracer:GeneratePath(origin.Position, velocity, function(arcPoint: ArcPoint)
			projectilePart.CFrame = CFrame.new(arcPoint.Origin, arcPoint.Origin + arcPoint.Direction) * spinCf;
			
			if arcTracer.AirSpin then
				spinCf = spinCf * CFrame.Angles(arcTracer.AirSpin, 0, 0);
			end;
			
			TweenService:Create(projectilePart, tweenInfo, {Position=arcPoint.Point;}):Play();

			if arcTracer.BindStepped then
				arcTracer:BindStepped(arcPoint, projectile);
			end
			if projectile.BindArcStepped then
				projectile:BindArcStepped(arcPoint);
			end
			if (arcPoint.Hit or arcPoint.LastPoint) and projectile.BindArcContact then
				return projectile:BindArcContact(arcPoint, arcTracer);
			end

			task.wait(delta);
			return;
		end);
		if projectile.BindArcComplete then
			projectile:BindArcComplete();
		end

		arcTracer.SpeedMultiplier = 1;
	end)

end

--MARK: ProjectileClass
local ProjectileInstances = {};
Projectile.Instances = ProjectileInstances;

function Projectile:Instance(projectilePackage)
	local arcTracer = modArcTracing.new();

	local projectile = {
		Id = nil;
		Part = nil;
		ToolHandler = nil;

		Properties = modPropertiesVariable.new(projectilePackage.Properties);
		ArcTracer = arcTracer;
	};
	
	local meta = {};
	function meta:__index(k)
		local v = rawget(self, k);
		if v ~= nil then
			return v;
		end

		if k == "CharacterClass" then
			local toolHandler = rawget(self, "ToolHandler");
			if toolHandler == nil then return nil; end;
			return toolHandler.CharacterClass;

		elseif k == "StorageItem" then
			local toolHandler = rawget(self, "ToolHandler");
			if toolHandler == nil then return nil; end;
			return toolHandler.StorageItem;

		end

		return projectilePackage[k];
	end

	function meta:Destroy()
		if self.IsDestroyed then return end;
		self.IsDestroyed = true;
		if self.BindDestroy then
			self:BindDestroy();
		end

		self.Properties:Destroy();
		self.ArcTracer:Destroy();

		Debugger.Expire(self.Part);
		self.Part = nil;
	end

	setmetatable(projectile, meta);

	if projectile.BindInstance then
		projectile:BindInstance();
	end

	return projectile;
end

function Projectile.get(projectileId)
	local projectilePool = ProjectilePools[projectileId];
	if projectilePool == nil then
		projectilePool = Projectile.load(projectileId);
	end;
	
	local projectileObject = Projectile.new(projectileId);
	return projectileObject;
end

function Projectile.new(projectileId)
	local projPool = ProjectilePools[projectileId];
	if projPool == nil then 
		projPool = Projectile.load(projectileId);
	end;

	if #projPool-2 <= 0 then
		local newProjectile = projPool.new();
		table.insert(projPool, newProjectile);
	end;
	
	local newProjectile = table.remove(projPool, 1);
	return newProjectile;
end

function Projectile.load(projectileId)
	if script:FindFirstChild(projectileId) == nil then
		Debugger:Warn("ProjectileId(",projectileId,") does not exist.");
		return;
	end;
	
	local projPool = ProjectilePools[projectileId];
	if projPool == nil then
		local projPackage = shared.require(script[projectileId]);
		local poolMeta = {};
		poolMeta.__index = poolMeta;

		function poolMeta.new()
			return Projectile:Instance(projPackage);
		end

		projPool = setmetatable({}, poolMeta);
		ProjectilePools[projectileId] = projPool;
	end;
	if #projPool > 0 then return end;
	
	local newProjectile = projPool.new();
	table.insert(projPool, newProjectile);
	return projPool;
end

--MARK: Projectile.fire
function Projectile.fire(projectileId, params)
	local projectilePool = ProjectilePools[projectileId];
	if projectilePool == nil then projectilePool = Projectile.load(projectileId) end;

	local originCFrame: CFrame = params.OriginCFrame;
	local toolHandler: ToolHandlerInstance = params.ToolHandler;
	local spreadedDirection: Vector3 = params.SpreadDirection;
	
	Projectile.ProjectileNum = Projectile.ProjectileNum + 1;

	local projectileInstance: ProjectileInstance = Projectile.new(projectileId);
	projectileInstance.Index = Projectile.ProjectileNum;
	projectileInstance.ToolHandler = toolHandler;
	projectileInstance.OriginCFrame = originCFrame;

	local properties: PropertiesVariable<anydict> = projectileInstance.Properties;

	local projectilePart = projectileInstance.Part;
	local projectileArcTracer = projectileInstance.ArcTracer;

	if projectileInstance.ArcTracerConfig then
		for k, v in pairs(projectileInstance.ArcTracerConfig) do
			if k == "Velocity" then continue end;
			if projectileArcTracer[k] == nil then
				Debugger:Warn(`Unknown ArcTracerConfig key {k}.`);
				continue;
			end
			projectileArcTracer[k] = v;
		end
	end

	CollectionService:AddTag(projectilePart, "Projectile");
	projectilePart.Anchored = false;
	projectilePart.CFrame = originCFrame or CFrame.new();
	if spreadedDirection then
		projectilePart.Velocity = spreadedDirection * (projectileArcTracer.Velocity or 50);
	end

	if toolHandler then
		local equipmentClass: EquipmentClass = toolHandler.EquipmentClass;
		local handlerConfigurations: ConfigVariable = equipmentClass.Configurations;

		for k, v in pairs(handlerConfigurations:GetKeyPairs()) do
			properties[k] = v;
		end

		if handlerConfigurations.ProjectileConfig then
			for k, v in pairs(handlerConfigurations.ProjectileConfig) do
				if k == "Velocity" then continue end;
				if projectileArcTracer[k] == nil then
					Debugger:Warn(`Unknown ProjectileConfig key {k}.`);
					continue;
				end
				projectileArcTracer[k] = v;
			end
		end
	end

	projectilePart.Parent = workspace.Entities;

	-- Despawning
	local despawnTime = properties.LifeTime or 10;
	local despawnScheduleJob: SchedulerJob? = nil;

	projectilePart:GetAttributeChangedSignal("DespawnTime"):Connect(function()
		despawnTime = projectilePart:GetAttribute("DespawnTime");
		if despawnTime == nil and despawnScheduleJob then
			ProjectileScheduler:Unschedule(despawnScheduleJob);
			despawnScheduleJob = nil;
			return;
		end;

		despawnScheduleJob = ProjectileScheduler:ScheduleFunction(function()
			projectileInstance:Destroy();
		end, tick()+despawnTime);
	end)

	projectilePart:SetAttribute("DespawnTime", tick()+despawnTime);
	--

	if projectilePart:CanSetNetworkOwnership() then
		projectilePart:SetNetworkOwner(nil);
	end

	if projectileInstance.BindFire then
		task.spawn(function()
			projectileInstance:BindFire();
		end)
	end

	return projectileInstance;
end




Projectile.Load = Projectile.load;
Projectile.New = Projectile.new;

return Projectile;