local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local CollectionService = game:GetService("CollectionService");

local localPlayer = game.Players.LocalPlayer;

local modToolHandler = shared.require(game.ReplicatedStorage.Library.ToolHandler);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modWeaponMechanics = shared.require(game.ReplicatedStorage.Library.WeaponsMechanics);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);

local projRaycast = RaycastParams.new();
projRaycast.FilterType = Enum.RaycastFilterType.Include;
projRaycast.IgnoreWater = true;
projRaycast.CollisionGroup = "Raycast";

local toolHandler: ToolHandler = modToolHandler.new();
--

function toolHandler.onRequire()
    remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler");
	if RunService:IsServer() then

	elseif RunService:IsClient() then
		modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
		modData = shared.require(game.Players.LocalPlayer:WaitForChild("DataModule") :: ModuleScript);

	end
end

function toolHandler.Init(handler: ToolHandlerInstance)
end

function toolHandler.Equip(handler: ToolHandlerInstance)
	local equipmentClass: EquipmentClass = handler.EquipmentClass;

	local configurations = equipmentClass.Configurations;
	local properties = equipmentClass.Properties;

	handler:LoadWieldConfig();
	Debugger:Warn(`Equip ({handler.WieldComp.ItemId})`);
end


if RunService:IsClient() then -- MARK: Client
	function toolHandler.ClientEquip(handler: ToolHandlerInstance)
		local modCharacter = modData:GetModCharacter();
		
		local mouseProperties = modCharacter.MouseProperties;
		local characterProperties = modCharacter.CharacterProperties;

		local storageItem = handler.StorageItem;
	
		local siid = storageItem.ID;
		local itemId = storageItem.ItemId;
		
		local toolPackage = handler.ToolPackage;
		local animations = toolPackage.Animations;
        local audio = toolPackage.Audio;

		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;
	
		local configurations: ConfigVariable = equipmentClass.Configurations;
		local properties: PropertiesVariable<{}> = equipmentClass.Properties;

        local projectileConfig = configurations.ProjectileConfig;

        local mainToolModel = handler.MainToolModel;
		local handle = mainToolModel and mainToolModel:WaitForChild("Handle") or nil;
        local initThrow = false;
        local throwChargeTick, progressBarTick;
        local throwChargeValue = 0;

        local playerClass = shared.modPlayers.get(localPlayer);
        --==
		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");

        if toolPackage.Animations.Load then
            toolAnimator:Play("Load", {
                FadeTime=0;
            });
            if toolPackage.OnAnimationPlay then
                task.defer(function()
                    toolPackage.OnAnimationPlay("Load", handler, mainToolModel);
                end)
            end
        end
        
        characterProperties.UseViewModel = false;
        if configurations.CustomViewModel then
            characterProperties.CustomViewModel = configurations.CustomViewModel;
        end

        
		local mechanicsBarElement: InterfaceElement = modClientGuis.getElement("MechanicsBar");
        local function updateProgressionBar(p)
            p = p or 0;
            if progressBarTick == nil or tick()-progressBarTick > 0.1 then
                progressBarTick = tick();

                if mechanicsBarElement then
                    mechanicsBarElement.ProgressValue = p;
                    mechanicsBarElement.ProgressType = "Throw";
                end
            end
        end
        
        local function reset()
            throwChargeTick = nil;
            progressBarTick = nil;
            throwChargeValue = 0;
            updateProgressionBar();
        end

        local function getImpactPoint(origin) -- reasonable throw ranges [25, 80];
            local rayWhitelist = CollectionService:GetTagged("TargetableEntities") or {};
            table.insert(rayWhitelist, workspace.Environment);
            table.insert(rayWhitelist, workspace.Characters);
            table.insert(rayWhitelist, workspace.Terrain);
            projRaycast.FilterDescendantsInstances = rayWhitelist;
    
            local velocity = (projectileConfig.Velocity + configurations.VelocityBonus * throwChargeValue);
    
            local scanPoint = modWeaponMechanics.CastHitscanRay{
                Origin = mouseProperties.Focus.p;
                Direction = mouseProperties.Direction;
                IncludeList = rayWhitelist;
                Range = velocity;
                RaycastParams = projRaycast;
            };
    
            local newDirection = (scanPoint-origin).Unit;
            local distance = (scanPoint-origin).Magnitude;
    
            -- Gets where player can hit.
            -- Get hitscan point from head using direction provided by crosshair hitscan.
            local impactPoint = modWeaponMechanics.CastHitscanRay{
                Origin = origin;
                Direction = newDirection;
                IncludeList = rayWhitelist;
                Range = distance;
                RaycastParams = projRaycast;
            };
    
            return impactPoint;
        end
    
        local throwOrigin = configurations.CustomThrowPoint and mainToolModel[configurations.CustomThrowPoint] or handle;
        local function primaryThrow()
            storageItem = modData.GetItemById(storageItem.ID);
            if storageItem == nil then return end;
            
            configurations.CanThrow = false;
            
            local throwCharge = throwChargeValue > 0.05 and throwChargeValue or 0;
    
            local head = playerClass.Head;

            local origin = handle.Position;
            local impactPoint = getImpactPoint(head.Position);
    
            toolAnimator:Play("Throw", {
                FadeTime=0.1;
            })
            
            if toolPackage.Audio.Throw then
                modAudio.PlayReplicated(audio.Throw.Id, throwOrigin);
            end
            
            task.delay(0.05, function()
                if toolPackage.Animations.Reload == nil then
                    throwOrigin.Transparency = 1;
                end
                
                if toolPackage.OnThrow then
                    toolPackage.OnThrow(handler);
                end
            end)
            remoteToolInputHandler:FireServer(modRemotesManager.Compress{
                Action = "action";
                Origin=origin;
                TargetPoint=impactPoint;
                ThrowCharge=throwCharge;
            });
            
            if storageItem.Quantity <= 1 and configurations.ConsumeOnThrow and properties.InfiniteAmmo == nil then
                
            else
                if toolPackage.Animations.Reload then
                    throwOrigin.Transparency = 0;
                    toolAnimator:Play("Reload");
                    if toolPackage.OnAnimationPlay then
                        task.defer(function()
                            toolPackage.OnAnimationPlay("Reload", handler);
                        end)
                    end
                end

                wait(configurations.ThrowRate or 0.2);
                throwOrigin.Transparency = 0;
                configurations.CanThrow = true;
                if toolPackage.OnThrowComplete then
                    toolPackage.OnThrowComplete(handler);
                end
            end
        end
        
        local quickThrown = false;
        local chargingAnimation: AnimationTrack? = nil;
        RunService:BindToRenderStep("ToolRender", Enum.RenderPriority.Character.Value, function()
            if modKeyBindsHandler:IsKeyDown("KeyFire") 
            and characterProperties.CanAction 
            and characterProperties.IsEquipped then
    
                if configurations.ChargeDuration then
                    if configurations.CanThrow then
                        if not initThrow then
                            if audio.Charge then
                                modAudio.PlayReplicated(audio.Charge.Id, handle);
                            end
                        end
                        initThrow = true;
                    end
                    if throwChargeTick == nil or not configurations.CanThrow then
                        throwChargeTick = tick();
                    else
                        throwChargeValue = math.clamp((tick()-throwChargeTick) / configurations.ChargeDuration, 0, 1);
                        if throwChargeValue > 0.05 then
                            if chargingAnimation == nil then
                                chargingAnimation = toolAnimator:Play("Charge", {
                                    FadeTime=0;
                                    PlaySpeed=0;
                                });
                            end
                            if chargingAnimation then
                                chargingAnimation.TimePosition = chargingAnimation.Length * math.min(throwChargeValue, 0.999);
                            end
                        end
                        updateProgressionBar(throwChargeValue);
    
                    end
                    characterProperties.Joints.WaistY = configurations.WaistRotation;
                    
                else
                    if configurations.CanThrow then
                        initThrow = true;
                    end
                end
                
            else
                if configurations.ChargeDuration then

                    if chargingAnimation then
                        if chargingAnimation.IsPlaying and initThrow then
                            chargingAnimation.TimePosition = chargingAnimation.Length;
                        end

                        chargingAnimation:AdjustSpeed(-2);
                        chargingAnimation:Stop(1);
                        chargingAnimation = nil;
                    end
                    
                    characterProperties.Joints.WaistY = 0;
                    
                    if initThrow then
                        initThrow = false;
                        primaryThrow();
                    end
    
                    reset();
                else
                    if initThrow and not quickThrown then
                        initThrow = false;
                        primaryThrow();
                        quickThrown = true;
                        delay(0.1, function()
                            quickThrown = false;
                        end)
                    end
                end
                
            end
        end)
        
        if toolPackage.OnToolEquip then
            task.defer(function()
                toolPackage.OnToolEquip(handler, mainToolModel);
            end)
        end
        
        delay(configurations.LoadTime or 0.5, function()
            configurations.CanThrow = true;
            if toolPackage.OnLoad then
                toolPackage.OnLoad(handler);
            end
        end)

	end

	function toolHandler.ClientUnequip(handler: ToolHandlerInstance)
	end


elseif RunService:IsServer() then -- MARK: Server
    local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
    local modProjectile = shared.require(game.ReplicatedStorage.Library.Projectile);

    function toolHandler.ActionEvent(handler: ToolHandlerInstance, packet)        
        local characterClass: CharacterClass = handler.CharacterClass;

        local origin = packet.Origin;
        local targetPoint = packet.TargetPoint;
        local throwCharge = packet.ThrowCharge;
        
        if typeof(origin) ~= "Vector3" then Debugger:Warn("Origin is not vector3"); return end;
        if typeof(targetPoint) ~= "Vector3" then Debugger:Warn("TargetPoint is not vector3"); return end;
        if typeof(throwCharge) ~= "number" then Debugger:Warn("ThrowCharge is not a number"); return end;

        if characterClass.HealthComp.IsDead then return end;        

        local storageItem: StorageItem = handler.StorageItem;
        local equipmentClass: EquipmentClass = handler.EquipmentClass;
        local configurations: ConfigVariable = equipmentClass.Configurations;
        local properties: PropertiesVariable<{}> = equipmentClass.Properties;
        
        local projectileConfig = configurations.ProjectileConfig;
        
        local toolModel = handler.Prefabs[1];
        local handle = toolModel.PrimaryPart;
            
        local distanceFromHandle = (handle.Position - origin).Magnitude;
        if distanceFromHandle > 10 then Debugger:Warn("Too far from handle."); return end;
        
        local itemLib = modItemsLibrary:Find(storageItem.ItemId);
        if storageItem.Quantity <= 0 then return end;
        
        local projectile: ProjectileInstance = modProjectile.fire(configurations.ProjectileId, {
            OriginCFrame = CFrame.new(origin);
            ToolHandler = handler;
        });

        throwCharge = math.clamp(throwCharge, 0, 1);

        local velocity;

        if projectileConfig.ThrowingMode == "Directional" then
            local dir = (targetPoint-origin).Unit;
            velocity = dir * projectileConfig.Velocity;

        else
            local velocityScalar = (projectileConfig.Velocity + configurations.VelocityBonus * throwCharge);
            local travelTime = (targetPoint-origin).Magnitude/velocityScalar;
            Debugger:StudioWarn("travelTime",travelTime, "velocityScalar", velocityScalar);
            velocity = projectile.ArcTracer:GetVelocityByTime(origin, targetPoint, travelTime);
            
        end

        modProjectile.serverSimulate(projectile, {
            Velocity = velocity;
        });
        
        if configurations.ConsumeOnThrow and properties.InfiniteAmmo == nil then
            if characterClass.ClassName == "PlayerClass" then
                local player: Player = (characterClass :: PlayerClass):GetInstance();

                local profile = shared.modProfile:Get(player);
                local inventory = profile.ActiveInventory;

                inventory:Remove(handler.StorageItem.ID, 1);
                shared.Notify(player, ("1 $Item removed from your Inventory."):gsub("$Item", itemLib.Name), "Negative");
            end
            
            if handler.StorageItem and handler.StorageItem.Quantity <= 0 then 
                handler.WieldComp:Unequip();
            end;
        end
    end

end

return toolHandler;