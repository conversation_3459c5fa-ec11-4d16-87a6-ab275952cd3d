local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
shared.waitForIgnition();
--==
local interactionRange = 15;

-- Variables;
local RunService = game:GetService("RunService");
local CollectionService = game:GetService("CollectionService");

local camera = workspace.CurrentCamera;
local localPlayer = game.Players.LocalPlayer;

local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modSyncTime = shared.require(game.ReplicatedStorage.Library.SyncTime);
local modEmotes = shared.require(game.ReplicatedStorage.Library.EmotesLibrary);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

local modVector = shared.require(game.ReplicatedStorage.Library.Util.Vector);

local character = script.Parent;
local humanoid = script.Parent:WaitForChild("Humanoid");
local rootPart = character:WaitForChild("HumanoidRootPart");
local animator = humanoid:WaitForChild("Animator");

for a=0, 10 do
	if workspace:IsAncestorOf(animator) then
		break;
	else
		task.wait();
	end
end
if not workspace:IsAncestorOf(animator) then return end;

local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);

local playerClassProperties: PropertiesVariable<{}> = playerClass.Properties;

local modCharacter = modData:GetModCharacter();
local characterProperties = modCharacter.CharacterProperties;

local UserInputService = game:GetService("UserInputService");
local mouseEnabled = UserInputService.MouseEnabled;
local touchEnabled = UserInputService.TouchEnabled;
local keyboardEnabled = UserInputService.KeyboardEnabled;
local mousePosition = UserInputService:GetMouseLocation();

local modInteractables = shared.require(game.ReplicatedStorage.Library.Interactables);

local remoteInteractionUpdate = modRemotesManager:Get("InteractionUpdate");

local hideIndicator = false;
local cooldownInteract = tick();
local cooldownDistanceCheck = tick();
local holdDuration = nil;

local ActivateInteract;

local interactAnimTracks = {};

local matchActive = false;
local matchExist = false;

local interactableAnimations = script:WaitForChild("Animations");

local activeInterface: Interface;
local indicatorElement: InterfaceElement;

-- Script;
local function clearInteractAnimations()
	for a=1, #interactAnimTracks do
		interactAnimTracks[a]:Stop();
		interactAnimTracks[a] = nil;
	end
end

local function loadInterface()
	if activeInterface ~= modClientGuis.ActiveInterface then
		activeInterface = modClientGuis.ActiveInterface;
	end

	indicatorElement = modClientGuis.getElement("InteractIndicatorElement");
	if indicatorElement == nil then return end;

	if indicatorElement.TouchButtonBeganFunc == nil then
		indicatorElement.TouchButtonBeganFunc = function()
			characterProperties.InteractionActive = true;
		end
	end

	if indicatorElement.TouchButtonEndedFunc == nil then
		indicatorElement.TouchButtonEndedFunc = function() 
			characterProperties.InteractionActive = false;
			clearInteractAnimations();
		end
	end
end

local function CheckEnabled(required)
	if required and type(required) == "table" then
		if required.Type == modInteractables.Types.Dialogue and modConfigurations.DisableDialogue then
			return;
		end
	else
		if type(required) ~= "table" then
			Debugger:Warn("Invalid interactable module returns.");
		end
	end
	return required;
end


local raycastParams = RaycastParams.new();
raycastParams.FilterType = Enum.RaycastFilterType.Include;
raycastParams.IgnoreWater = true;
raycastParams.CollisionGroup = "Raycast";


local function canInteract()
	local v = characterProperties.CanInteract;

	if activeInterface and activeInterface.Properties.DisableInteractables == true then
		v = false;
	end
	
	return v;
end

--MARK: Indicator properties;
local function updateIndicator(properties)
	if indicatorElement == nil then return end;

	for k, v in pairs(properties) do
		if k == "LabelText" then
			indicatorElement.LabelText = v;

		elseif k == "ButtonText" then
			indicatorElement.ButtonText = v;

		elseif k == "Size" then
			indicatorElement.IndicatorLabel.Size = v;

		end
	end
end

local function setIndicatorPos(x, y)
	if indicatorElement == nil then return end;
	indicatorElement.IndicatorLabel.Position = UDim2.new(0, x, 0, y);
end

local function setIndicatorVisible(v)
	if indicatorElement == nil then return end;
	indicatorElement.IndicatorLabel.Visible = v;
end

local function getsetProgressBarVisible(v)
	if indicatorElement == nil then return end;

	if v ~= nil then
		indicatorElement.ProgressBarVisible = v;
	end
	
	return indicatorElement.ProgressBarVisible;
end

local function updateRadial(v)
	if indicatorElement == nil then return end;
	indicatorElement.IndicatorRadial:UpdateLabel(v);
end

local function findInteractable(rayHit)
	local interactConfig;

	-- Find Interactable obj
	if rayHit:FindFirstChild("Interactable") then
		interactConfig = rayHit.Interactable;
		
	else
		if rayHit.Parent:FindFirstChild("Interactable") 
		and (rayHit.Parent.Interactable:IsA("ModuleScript") or rayHit.Parent.Interactable:IsA("Configuration")) then
			interactConfig = rayHit.Parent.Interactable;
			
		elseif rayHit.Parent:GetAttribute("InteractableParent") then
			local model = rayHit.Parent;
			while model:GetAttribute("InteractableParent") do
				model = model.Parent;
			end
			
			if model.PrimaryPart and model:FindFirstChild("Interactable") 
			and (model.Interactable:IsA("ModuleScript") or model.Interactable:IsA("Configuration")) then
				interactConfig = model.Interactable;
				rayHit = model.PrimaryPart;
			end
			
		elseif rayHit.Parent:IsA("Accessory") and rayHit.Parent.Parent:FindFirstChild("Interactable") 
		and (rayHit.Parent.Parent.Interactable:IsA("ModuleScript")
			or rayHit.Parent.Parent.Interactable:IsA("Configuration")) then
			interactConfig = rayHit.Parent.Parent.Interactable;
		end
		
	end

	return interactConfig;
end
--===

local heartbeatSkip = tick();
local autoTriggerDelay = tick();
local maxDist = 32;

-- MARK: RunService.RenderStepped
RunService.RenderStepped:Connect(function(delta)
	if localPlayer.GameplayPaused then return end;

	if activeInterface ~= modClientGuis.ActiveInterface then
		loadInterface();
	end

	local beatTick = tick();

	if heartbeatSkip > beatTick then return end;
	if Debugger.ClientFps <= 30 then
		heartbeatSkip = beatTick+delta;
	elseif Debugger.ClientFps <= 15 then
		heartbeatSkip = beatTick+(delta*2);
	end

	mousePosition = UserInputService:GetMouseLocation();
	
	if not hideIndicator and canInteract() and not playerClass.HealthComp.IsDead then
		local pointRay = camera:ViewportPointToRay(mousePosition.X, mousePosition.Y);
		if not mouseEnabled and touchEnabled then
			pointRay = camera:ViewportPointToRay(camera.ViewportSize.X/2, camera.ViewportSize.Y/2);
		end
		
		local rayWhitelist = {
			workspace.Environment; 
			workspace.Entity; 
			workspace.Terrain; 
			workspace:FindFirstChild("Characters");
		};
		if characterProperties.RayIgnoreInteractables ~= true then
			table.insert(rayWhitelist, workspace.Interactables);
		end
		
		raycastParams.FilterDescendantsInstances = rayWhitelist;
		local raycastResult = workspace:Raycast(pointRay.Origin, pointRay.Direction*maxDist, raycastParams)
		local rayHit, rayPoint, rayNormal, distance;
		
		if raycastResult then
			rayHit, rayPoint, rayNormal = raycastResult.Instance, raycastResult.Position, raycastResult.Normal;
			distance = localPlayer:DistanceFromCharacter(rayPoint);

		else
			rayPoint = pointRay.Origin + pointRay.Direction*maxDist;
			distance = math.huge;

		end
		
		if rayHit then
			characterProperties.InteractRayHit = rayHit;
			characterProperties.InteractAimPoint = rayPoint;
			characterProperties.InteractAimNormal = Vector3.new(
				math.clamp(rayNormal.X, -1, 1),
				math.clamp(rayNormal.Y, -1, 1),
				math.clamp(rayNormal.Z, -1, 1)
			);
		else
			characterProperties.InteractRayHit = nil;
			characterProperties.InteractAimPoint = pointRay.Origin;
			characterProperties.InteractAimNormal = nil;
		end
		characterProperties.InteractAimOrigin = pointRay.Origin;
		characterProperties.InteractAimDirection = pointRay.Direction;
		characterProperties.InteractDistance = distance;
		
		if rayHit ~= nil then
			local interactConfig;
			local skipUpdate = false;
			
			local function loadInteractable(interactData)
				local newInteraction = CheckEnabled(interactData);
				
				if interactConfig and interactConfig:GetAttribute("Disabled") == true then
					newInteraction = nil;
				end
				
				if characterProperties.ActiveInteract 
				and characterProperties.ActiveInteract.CaptureHold 
				and characterProperties.InteractionActive then
					skipUpdate = true;
					return;
				end
				
				if newInteraction then
					if characterProperties.ActiveInteract == nil or newInteraction.CountId ~= characterProperties.ActiveInteract.CountId then
						holdDuration = nil;
						characterProperties.InteractAlpha = 0;
						
						local dataMeta = getmetatable(newInteraction);
						dataMeta.CharacterModule = modCharacter;
						dataMeta.Humanoid = modCharacter.Humanoid;
						dataMeta.RootPart = modCharacter.RootPart;
						
						newInteraction.Script = interactConfig;
						newInteraction:SyncRequest();
						
						characterProperties.ActiveInteract = newInteraction;
						if interactConfig and interactConfig:IsA("ModuleScript") then
							rayHit = modInteractables.getPartOfInteractableConfig(interactConfig);
							characterProperties.ActiveInteract.Part = rayHit;
						end
					end
				else
					characterProperties.ActiveInteract = nil;
					playerClassProperties.RayHitInteractable = nil;
				end
			end
			
			playerClassProperties.RayHitInteractable = findInteractable(rayHit);

			if skipUpdate == true then
				
			elseif characterProperties.ProxyInteractable then

				rayHit = characterProperties.ProxyInteractable.Part;
				if characterProperties.ProxyInteractable.Part then
					loadInteractable(characterProperties.ProxyInteractable);
				else
					if Debugger.ProxyMissingPartTick == nil or tick()-Debugger.ProxyMissingPartTick > 1 then
						Debugger:Warn(`ProxyInteractable missing Part.`);
					end
				end
				
			else
				-- Find Interactable obj
				interactConfig = playerClassProperties.RayHitInteractable;

				-- instance interactable
				if interactConfig then
					if interactConfig:IsA("ModuleScript") then
						loadInteractable(require(interactConfig));

					elseif interactConfig:IsA("Configuration") then
						local interactData = modInteractables.getOrNew(interactConfig);
						if interactData then
							loadInteractable(interactData);
						end

					end
				end

				if interactConfig == nil and characterProperties.ActiveInteract ~= nil then
					if characterProperties.ActiveInteract.CanInteract == false 
						or characterProperties.ActiveInteract.Disabled 
						or characterProperties.ActiveInteract.IndicatorPresist ~= true then
						
						local clearInteract = true;
						if characterProperties.ActiveInteract.CaptureHold and characterProperties.InteractionActive then
							clearInteract = false;
						end
						
						if clearInteract then
							characterProperties.ActiveInteract = nil;
							playerClassProperties.RayHitInteractable = nil;
						end
					end
				end
			end
		end
		
		if characterProperties.ActiveInteract ~= nil and characterProperties.ActiveInteract.Part then
			local activeObj = characterProperties.ActiveInteract.Part;
			if characterProperties.ActiveInteract.ProxyObject then
				activeObj = characterProperties.ActiveInteract.ProxyObject;
			end

			local indicatorPos;
			if activeObj:IsA("PVInstance") then
				indicatorPos = activeObj:GetPivot().Position - activeObj.PivotOffset.Position;
			elseif activeObj:IsA("Attachment") then
				indicatorPos = activeObj.WorldPosition;
			end
			if characterProperties.ActiveInteract.ProxyOffset then
				indicatorPos = indicatorPos +characterProperties.ActiveInteract.ProxyOffset;
			end
			
			if characterProperties.ActiveInteract.Distance == nil or (beatTick - cooldownDistanceCheck) > 0.1 then
				cooldownDistanceCheck = tick();
				characterProperties.ActiveInteract.Distance = localPlayer:DistanceFromCharacter(indicatorPos);
				characterProperties.ActiveInteract.Reachable = characterProperties.ActiveInteract.Distance <= (characterProperties.ActiveInteract.InteractableRange or interactionRange);
			end
			
			if characterProperties.ActiveInteract.Reachable then
				local screenPoint, inFront = camera:WorldToViewportPoint(indicatorPos);
				if inFront and characterProperties.ActiveInteract.ShowIndicator ~= false then
					
					if tick()-autoTriggerDelay >= 5 then
						characterProperties.ActiveInteract:Trigger();
					end
					if characterProperties.ActiveInteract == nil then return end;
					
					if characterProperties.ActiveInteract.Disabled == nil then
						local prefix = "";
						
						if characterProperties.ActiveInteract.CanInteract and characterProperties.ActiveInteract.InteractDuration then
							prefix = "[Hold] "
						end 
						
						if characterProperties.ActiveInteract.Type == "Hold" then
							updateIndicator{
								LabelText = "[Hold] "..(characterProperties.ActiveInteract.Label or "Interact");
							};
						else
							updateIndicator{
								LabelText = prefix..(characterProperties.ActiveInteract.Label or "Interact");
							};
						end
					end
					
					if characterProperties.ActiveInteract.Disabled then
						updateIndicator{
							ButtonText = "";
							Size = UDim2.new(0, 10, 0, 10);
							LabelText = characterProperties.ActiveInteract.Disabled;
						};
						
					elseif characterProperties.ActiveInteract.CanInteract == false then
						updateIndicator{
							ButtonText = "";
							Size = UDim2.new(0, 10, 0, 10);
						};

					else
						local buttonText = "";
						local buttonSize = UDim2.new(0, 50, 0, 50);
						
						if modCharacter.CharacterProperties.ControllerEnabled then
							buttonText = "X";
							
						elseif keyboardEnabled then
							local keyString = tostring(modData.Settings["KeyInteract"] or "E");
							if #keyString >= 5 then
								keyString = string.gsub(keyString, "[^A-Z,0-9]", "")
							end
							buttonText = keyString;
							
						elseif touchEnabled then
							buttonText = "Tap";
							buttonSize = UDim2.new(0, 100, 0, 50);
							
						elseif mouseEnabled then
							buttonText = "Click";
							buttonSize = UDim2.new(0, 100, 0, 50);
							
						end

						updateIndicator{
							ButtonText = buttonText;
							Size = buttonSize;
						};

					end
					
					
					if not touchEnabled or getsetProgressBarVisible() == false then
						local lerpVecPos = Vector2.new(screenPoint.X, screenPoint.Y);
						setIndicatorPos(lerpVecPos.X, lerpVecPos.Y);
					end
					
					setIndicatorVisible(true);
				else
					setIndicatorVisible(false);
				end
			else
				setIndicatorVisible(false);
				characterProperties.ClearInteractHold();
				if matchExist then
					matchExist = false;
					matchActive = false;
					remoteInteractionUpdate:FireServer(nil, nil, "stop")
				end
			end
		else
			setIndicatorVisible(false);
		end
	else
		setIndicatorVisible(false);
	end
	
	if characterProperties.ActiveInteract == nil then
		characterProperties.ClearInteractHold();
		if matchExist then
			matchExist = false;
			matchActive = false;
			remoteInteractionUpdate:FireServer(nil, nil, "stop")
		end
	else
		matchExist = true;
	end
	
	if characterProperties.InteractionActive then
		ActivateInteract(delta);
		
	else
		getsetProgressBarVisible(false);
		holdDuration = nil;
		characterProperties.InteractAlpha = 0;
		characterProperties.CharacterInteracting = false;
		
		if matchActive ~= characterProperties.InteractionActive then
			matchActive = characterProperties.InteractionActive;
			remoteInteractionUpdate:FireServer(nil, nil, "stop")
		end
	end;
end)


-- !outline: function characterProperties.ClearInteractHold()
characterProperties.ClearInteractHold = function()
	holdDuration = nil;
	characterProperties.InteractAlpha = 0;
	characterProperties.InteractGyro = nil;
	clearInteractAnimations();
end;


-- !outline: function ActivateInteract()
function ActivateInteract(delta)
	local inputTick = tick();
	
	local interactObject = characterProperties.ActiveInteract;
	if interactObject and interactObject.Disabled then return end;
	if canInteract() 
		and interactObject ~= nil 
		and (interactObject.Distance or math.huge) <= (interactObject.InteractableRange or interactionRange) then

		local function interact()
			cooldownInteract = inputTick;
			
			if interactObject ~= nil then
				hideIndicator = true;
				modKeyBindsHandler:Debounce("KeyInteract");
				
				if interactObject:Interact() then
					characterProperties.ActiveInteract = nil;
					playerClassProperties.RayHitInteractable = nil;
				end;
				hideIndicator = false;
			end
		end
		
		if interactObject.CanInteract then
			if matchActive ~= characterProperties.InteractionActive then
				matchActive = characterProperties.InteractionActive;
				remoteInteractionUpdate:FireServer(interactObject.Config, interactObject.Part, "start")
			end
			
			local animationId = interactObject.Animation;
			if animationId == nil then
				if interactObject.Type == "Trigger" then
					if interactObject.InteractDuration and interactObject.InteractDuration >= 1 then
						animationId = "InspectCrate";
					else
						animationId = "Press";
					end
					
				end
				
			end
			
			if animationId then
				--if modCharacter.CurrentAnimation ~= animationId then
				--	playEmote(animationId);
				--end
				
				if #interactAnimTracks <= 0 then
					local animLib = modEmotes:Find(animationId);
					if animLib then
						table.insert(interactAnimTracks, animator:LoadAnimation(animLib.Animation));
					end;
					
					local interactAnimation = interactableAnimations:FindFirstChild(animationId);
					if interactAnimation then
						local interactableAnimator = interactObject.Part.Parent:FindFirstChild("Animator", true);
						if interactableAnimator then
							table.insert(interactAnimTracks, interactableAnimator:LoadAnimation(interactAnimation));
						end
					end
				end
			end
			
			
			local interactPoint = interactObject.Part:FindFirstChild("InteractPoint");
			if interactObject.Part.Name == "UpperTorso" then
				local newInteractionPoint = interactObject.Part.Parent:FindFirstChild("InteractPoint", true);
				if newInteractionPoint then
					interactPoint = newInteractionPoint
				end
			end
			if interactPoint then
				humanoid:MoveTo(interactPoint.WorldPosition + Vector3.new(0, math.random(1, 10)/1000, 0));
			end
			
			if interactObject.Type == "Hold" then
				getsetProgressBarVisible(true);

				if typeof(interactObject.CaptureHold) == "number" then
					if characterProperties.InteractDistance > interactObject.CaptureHold then
						characterProperties.ClearInteractHold();
						updateRadial(0);
						characterProperties.CharacterInteracting = false;
						return;
					end
				end
				updateRadial(1);

				for a=1, #interactAnimTracks do
					interactAnimTracks[a].TimePosition = math.clamp(interactAnimTracks[a].TimePosition + delta, 0, interactAnimTracks[a].Length-0.01);
				end
				return;
			end	
			
			if interactObject.InteractDuration then
				if interactPoint then
					local dist = (interactPoint.WorldPosition-rootPart.Position).Magnitude;
					if dist > 3 then
						characterProperties.ClearInteractHold();
						updateRadial(0);
						characterProperties.CharacterInteracting = false;
						return;
					end
					
					characterProperties.InteractGyro = interactPoint.WorldCFrame;
				end
				
				if animationId and modCharacter.EquippedItem and modCharacter.EquippedItem.ID then
					if interactObject.ItemRequired ~= modCharacter.EquippedItem.ItemId then
						modData.HandleTool("unequip", {Siid=modCharacter.EquippedItem.ID;});
					end
				end
				
				if holdDuration == nil then
					holdDuration = inputTick;
					if interactObject.OnStartInteract then
						interactObject:OnStartInteract();
					end
				end;
				
				getsetProgressBarVisible(true);
				characterProperties.CharacterInteracting = true;
				local alpha = math.clamp(((tick() - holdDuration) or 0)/interactObject.InteractDuration, 0, 20);
				
				characterProperties.InteractAlpha = alpha;
				
				if alpha >= 0.05 then
					for a=1, #interactAnimTracks do
						local track = interactAnimTracks[a];
						if track.Length > 0 then
							if not track.IsPlaying then
								track:Play(nil, nil, 0);
							end
							track.TimePosition = math.clamp(alpha * track.Length, 0, track.Length-0.01);
						end
					end
				end
				
				if alpha >= 1 then
					updateRadial(1);

					holdDuration = nil;
					characterProperties.InteractionActive = false;
					characterProperties.RefreshTransparency = true;
					characterProperties.InteractAlpha = 0;
					interact();
				else
					updateRadial(alpha);
					
				end
				
			elseif (inputTick - cooldownInteract) > 0.5 then
				interact();
				
			end
		end
	end
end

function modData.InteractRequest(interactableModule, object)
	if interactableModule == nil then return end;
	
	local newInteraction = CheckEnabled(require(interactableModule));
	if newInteraction then
		newInteraction.Script = interactableModule;
		newInteraction.Humanoid = modCharacter.Humanoid;
		newInteraction.RootPart = modCharacter.RootPart;
		newInteraction.CharacterModule = modCharacter;
		newInteraction:Trigger();
		
		newInteraction.IndicatorPresist = false;
		newInteraction:Interact();
	end
end

-- !outline: signal UserInputService.InputBegan
UserInputService.InputBegan:connect(function(inputObject, inputEvent)
	if UserInputService:GetFocusedTextBox() ~= nil then return end;
	if modKeyBindsHandler:Match(inputObject, "KeyInteract") then
		characterProperties.InteractionActive = true;
	end
	
	if modBranchConfigs.CurrentBranch.Name == "Dev" then
		if characterProperties.ActiveInteract and script:GetAttribute("Debug") == true then
			Debugger:Log("ActiveInteract: ", characterProperties.ActiveInteract)
		end
	end
end)

-- !outline: signal UserInputService.InputEnded
UserInputService.InputEnded:Connect(function(inputObject, inputEvent)
	--if UserInputService:GetFocusedTextBox() ~= nil then return end;
	if modKeyBindsHandler:Match(inputObject, "KeyInteract") then
		characterProperties.InteractionActive = false;
		clearInteractAnimations();
		characterProperties.ActiveInteract = nil;
		playerClassProperties.RayHitInteractable = nil;
		
		if humanoid.RootPart then
			humanoid:MoveTo(humanoid.RootPart.Position);
		end
	end
end)

-- !outline: modData.TouchInteract(touchPart: BasePart)
function modData.TouchInteract(touchPart: BasePart)
	if canInteract() and not playerClass.HealthComp.IsDead then
		if touchPart.Parent == nil then return end;
		local objectModel = touchPart.Parent:IsA("Model") and touchPart.Parent or nil;
		local modelPrimary = objectModel and objectModel.PrimaryPart or nil;
		local interactModule = objectModel and objectModel:FindFirstChild("Interactable");

		if modelPrimary and interactModule and interactModule:IsA("ModuleScript") then
			local interactData = CheckEnabled(require(interactModule));

			if interactData == nil or interactData.TouchInteract ~= true then return end;
			if interactData.TouchPickUp == false then return end;
			local dTouchInteractAttribute = interactModule:GetAttribute("DisableTouchInteract");
			if dTouchInteractAttribute and modSyncTime.GetTime() < dTouchInteractAttribute then return end;

			if interactData.Type == "Pickup" and interactData.ForceTouchPickup ~= true then
				if modData.Settings and modData.Settings.AutoPickupMode == 2 then
					return;

				elseif modData.Settings and modData.Settings.AutoPickupMode == 1 then
					local pickUpEnabled = modData.PickupCache[interactData.ItemId];

					if pickUpEnabled ~= true then
						return;
					end

				else
					if interactData.TouchPickUp == false then
						return;
					end

				end
			end

			interactData.Script = interactModule;
			interactData.Humanoid = modCharacter.Humanoid;
			interactData.RootPart = modCharacter.RootPart;
			interactData.CharacterModule = modCharacter;
			interactData.IndicatorPresist = false;
			interactData:Interact();

			characterProperties.ActiveInteract = nil;
			playerClassProperties.RayHitInteractable = nil;
		end
	end
end

-- !outline: signal humanoid.Touched
humanoid.Touched:Connect(modData.TouchInteract)

local overlapInteractParam = OverlapParams.new();

overlapInteractParam.FilterType = Enum.RaycastFilterType.Include;
overlapInteractParam.MaxParts = 10;


modSyncTime.GetClock():GetPropertyChangedSignal("Value"):Connect(function()
	if modData:IsMobile() then return end;

	local interactableList = CollectionService:GetTagged("Interactable");

	for a=1, #interactableList do
		local config = interactableList[a];
		
		local interactable: InteractableInstance;

		local point = config:GetAttribute("_Point");
		if point == nil then 
			interactable = modInteractables.getOrNew(config);
		end;
		if point == nil or not modVector.InCenter(point, camera.CFrame.Position, 64) then continue end;

		interactable = modInteractables.getOrNew(config);
		
		point = config:GetAttribute("_Point");
		if point == nil then continue end;
		if not modVector.InCenter(point, camera.CFrame.Position, 64) then continue end;

		if interactable.LastProximityTrigger == nil or tick()-interactable.LastProximityTrigger >= 5 then
			interactable.LastProximityTrigger = tick();

			if interactable.Trigger then
				interactable:Trigger();
			end
		end
	end
end)